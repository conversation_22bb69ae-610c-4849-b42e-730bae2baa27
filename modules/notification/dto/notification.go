package dto

import "time"

// EmailRequest đại diện cho yêu cầu gửi email
type EmailRequest struct {
	To          []string               `json:"to" binding:"required"`
	Subject     string                 `json:"subject" binding:"required"`
	Content     string                 `json:"content" binding:"required"`
	Body        string                 `json:"body,omitempty"` // Alternative to Content
	HTMLContent string                 `json:"html_content,omitempty"`
	From        string                 `json:"from,omitempty"`
	CC          []string               `json:"cc,omitempty"`
	BCC         []string               `json:"bcc,omitempty"`
	Template    string                 `json:"template,omitempty"`
	Variables   map[string]interface{} `json:"variables,omitempty"`
	UserID      string                 `json:"user_id,omitempty"` // For tracking purposes
}

// SMSRequest đại diện cho yêu cầu gửi SMS
type SMSRequest struct {
	To        []string               `json:"to" binding:"required"`
	Message   string                 `json:"message" binding:"required"`
	Template  string                 `json:"template,omitempty"`
	Variables map[string]interface{} `json:"variables,omitempty"`
	UserID    string                 `json:"user_id,omitempty"` // For tracking purposes
}

// PushRequest đại diện cho yêu cầu gửi push notification
type PushRequest struct {
	To        []string               `json:"to" binding:"required"`
	UserID    string                 `json:"user_id,omitempty"` // For user-specific push notifications
	Title     string                 `json:"title" binding:"required"`
	Body      string                 `json:"body" binding:"required"`
	Icon      string                 `json:"icon,omitempty"`
	Badge     int                    `json:"badge,omitempty"`
	Sound     string                 `json:"sound,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Template  string                 `json:"template,omitempty"`
	Variables map[string]interface{} `json:"variables,omitempty"`
}

// NotificationResponse đại diện cho phản hồi khi gửi thông báo
type NotificationResponse struct {
	ID         string    `json:"id"`
	Type       string    `json:"type"`
	Status     string    `json:"status"`
	Message    string    `json:"message,omitempty"`
	SentAt     time.Time `json:"sent_at"`
	Recipients int       `json:"recipients"`
}

// NotificationHistory đại diện cho lịch sử thông báo
type NotificationHistory struct {
	ID            string                 `json:"id"`
	UserID        string                 `json:"user_id"`
	Type          string                 `json:"type"`
	Title         string                 `json:"title,omitempty"`
	Message       string                 `json:"message"`
	Recipient     string                 `json:"recipient"`
	Subject       string                 `json:"subject,omitempty"`
	Content       string                 `json:"content"`
	Status        string                 `json:"status"`
	SentAt        time.Time              `json:"sent_at"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	DeliveredAt   *time.Time             `json:"delivered_at,omitempty"`
	FailureReason string                 `json:"failure_reason,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}
