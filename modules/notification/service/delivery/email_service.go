package delivery

import (
	"context"
	"fmt"
	"log"
	"net/smtp"
	"strings"
	"time"

	"github.com/webnew/wn-backend-v2/modules/notification/configs"
	"github.com/webnew/wn-backend-v2/modules/notification/models"
)

// EmailService implements email notification delivery
type EmailService struct {
	config configs.EmailConfig
}

// NewEmailService creates a new email delivery service
func NewEmailService(config configs.EmailConfig) *EmailService {
	return &EmailService{
		config: config,
	}
}

// Send sends a notification via email
func (s *EmailService) Send(ctx context.Context, notification *models.Notification, userID uint) error {
	log.Println("SendEmail", notification)
	// Get the SMTP configuration
	smtpConfig := s.config.SMTP
	smtpAddr := fmt.Sprintf("%s:%d", smtpConfig.Host, smtpConfig.Port)

	// Set up authentication if credentials are provided
	var auth smtp.Auth
	if smtpConfig.Username != "" && smtpConfig.Password != "" {
		auth = smtp.PlainAuth("", smtpConfig.Username, smtpConfig.Password, smtpConfig.Host)
	}

	// Build email headers
	from := s.config.FromAddress
	if s.config.FromName != "" {
		from = fmt.Sprintf("%s <%s>", s.config.FromName, s.config.FromAddress)
	}

	// We would normally get the recipient's email from a user service
	// For testing, we'll send to a test email
	to := []string{"<EMAIL>"}

	// Build the email message
	headers := make(map[string]string)
	headers["From"] = from
	headers["To"] = strings.Join(to, ", ")
	headers["Subject"] = notification.Title
	headers["MIME-Version"] = "1.0"
	headers["Content-Type"] = "text/html; charset=UTF-8"
	headers["Date"] = time.Now().Format(time.RFC1123Z)

	if s.config.ReplyTo != "" {
		headers["Reply-To"] = s.config.ReplyTo
	}

	// Construct message
	var message strings.Builder
	for key, value := range headers {
		message.WriteString(fmt.Sprintf("%s: %s\r\n", key, value))
	}

	// Add empty line between headers and body
	message.WriteString("\r\n")

	// Add message body
	message.WriteString(notification.Content)

	// Send email
	err := smtp.SendMail(
		smtpAddr,
		auth,
		s.config.FromAddress,
		to,
		[]byte(message.String()),
	)

	log.Println("SendEmail", err)

	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	fmt.Printf("Email sent to %s: %s\n", strings.Join(to, ", "), notification.Title)
	return nil
}

// GetChannelCode returns the channel code for email
func (s *EmailService) GetChannelCode() string {
	return "email"
}
