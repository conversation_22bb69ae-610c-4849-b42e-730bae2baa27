package delivery

import (
	"context"
	"fmt"

	"github.com/webnew/wn-backend-v2/modules/notification/configs"
	"github.com/webnew/wn-backend-v2/modules/notification/models"
)

// SMSService implements SMS notification delivery
type SMSService struct {
	config configs.SMSConfig
}

// NewSMSService creates a new SMS delivery service
func NewSMSService(config configs.SMSConfig) *SMSService {
	return &SMSService{
		config: config,
	}
}

// Send sends a notification via SMS
func (s *SMSService) Send(ctx context.Context, notification *models.Notification, userID uint) error {
	// In a real implementation, this would use the configured SMS provider
	// to send an SMS to the user

	// For now, we'll just log the SMS that would be sent
	fmt.Printf("Would send SMS notification to user %d: %s\n",
		notification.UserID, notification.Title)

	return nil
}

// GetChannelCode returns the channel code for SMS
func (s *SMSService) GetChannelCode() string {
	return "sms"
}
