package delivery

import (
	"context"
	"fmt"
	"time"

	"github.com/webnew/wn-backend-v2/modules/notification/models"
	"github.com/webnew/wn-backend-v2/modules/notification/repository"
)

// InAppService implements in-app notification delivery
type InAppService struct {
	notificationRepo repository.NotificationRepository
}

// NewInAppService creates a new in-app delivery service
func NewInAppService(notificationRepo repository.NotificationRepository) *InAppService {
	return &InAppService{
		notificationRepo: notificationRepo,
	}
}

// Send marks a notification as sent for in-app delivery
func (s *InAppService) Send(ctx context.Context, notification *models.Notification) error {
	// For in-app notifications, we just need to mark the notification as sent
	notification.IsSent = true
	now := time.Now()
	notification.SentAt = &now

	// Update the notification in the database
	err := s.notificationRepo.Update(ctx, notification)
	if err != nil {
		return fmt.Errorf("failed to mark in-app notification as sent: %w", err)
	}

	return nil
}

// GetChannelCode returns the channel code for in-app notifications
func (s *InAppService) GetChannelCode() string {
	return "in_app"
}
