package delivery

import (
	"context"

	"github.com/webnew/wn-backend-v2/modules/notification/models"
)

// DeliveryService defines the interface for notification delivery methods
type DeliveryService interface {
	// Send delivers a notification through a specific channel
	Send(ctx context.Context, notification *models.Notification, userID int) error

	// GetChannelCode returns the channel code this delivery service handles
	GetChannelCode() string
}
