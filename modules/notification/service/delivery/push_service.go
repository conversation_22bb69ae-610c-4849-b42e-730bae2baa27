package delivery

import (
	"context"
	"fmt"

	"github.com/webnew/wn-backend-v2/modules/notification/configs"
	"github.com/webnew/wn-backend-v2/modules/notification/models"
)

// PushService implements push notification delivery
type PushService struct {
	config configs.PushConfig
}

// NewPushService creates a new push notification delivery service
func NewPushService(config configs.PushConfig) *PushService {
	return &PushService{
		config: config,
	}
}

// Send sends a notification via push notification
func (s *PushService) Send(ctx context.Context, notification *models.Notification, userID uint) error {
	// In a real implementation, this would use the configured push notification provider
	// to send a push notification to the user

	// For now, we'll just log the push notification that would be sent
	fmt.Printf("Would send push notification to user %d: %s\n",
		notification.UserID, notification.Title)

	return nil
}

// GetChannelCode returns the channel code for push notifications
func (s *PushService) GetChannelCode() string {
	return "push"
}
