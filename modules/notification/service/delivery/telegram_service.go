package delivery

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strings"

	"github.com/webnew/wn-backend-v2/modules/notification/configs"
	"github.com/webnew/wn-backend-v2/modules/notification/models"
)

// TelegramService implements Telegram notification delivery
type TelegramService struct {
	config configs.TelegramConfig
}

// NewTelegramService creates a new Telegram delivery service
func NewTelegramService(config configs.TelegramConfig) *TelegramService {
	return &TelegramService{
		config: config,
	}
}

// Send sends a notification via Telegram
func (s *TelegramService) Send(ctx context.Context, notification *models.Notification, userID uint) error {
	log.Println("SendTelegram", notification)

	// Check if bot token is configured
	if s.config.BotToken == "" {
		return fmt.Errorf("telegram bot token not configured")
	}

	// In a real implementation, you would get the user's Telegram chat ID from a user service
	// For testing, we'll use a default chat ID if provided in config
	chatID := s.config.DefaultChatID
	if chatID == "" {
		return fmt.Errorf("telegram chat ID not found for user %d", userID)
	}

	// Prepare message text
	messageText := notification.Title
	if notification.Content != "" {
		messageText = fmt.Sprintf("%s\n\n%s", notification.Title, notification.Content)
	}

	// Build the Telegram Bot API URL
	apiURL := fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", s.config.BotToken)

	// Prepare request parameters
	params := url.Values{}
	params.Set("chat_id", chatID)
	params.Set("text", messageText)
	params.Set("parse_mode", "HTML")

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, strings.NewReader(params.Encode()))
	if err != nil {
		return fmt.Errorf("failed to create telegram request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send telegram message: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("telegram API returned non-OK status: %d", resp.StatusCode)
	}

	return nil
}

// GetChannelCode returns the channel code this delivery service handles
func (s *TelegramService) GetChannelCode() string {
	return models.ChannelTelegram
}
