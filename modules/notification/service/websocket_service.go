// Package service implements the business logic for the notification module
package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/webnew/wn-backend-v2/modules/notification/models"
	"github.com/webnew/wn-backend-v2/modules/notification/repository"
	"github.com/webnew/wn-backend-v2/modules/notification/repository/redis"
)

// WebsocketMessage represents a message sent over WebSocket
type WebsocketMessage struct {
	Type    string      `json:"type"`
	Topic   string      `json:"topic"`
	Payload interface{} `json:"payload"`
}

// WebSocketService handles WebSocket connections and message distribution
type WebSocketService struct {
	websocketRepo   repository.WebsocketRepository
	websocketPubSub *redis.WebsocketPubSub
	connections     map[string]*WebSocketConnection
	userConnections map[int][]string // Maps userID to connection IDs
	mu              sync.RWMutex
}

// WebSocketConnection represents a client connection
type WebSocketConnection struct {
	ID       string
	UserID   int
	Send     chan []byte
	LastPing time.Time
}

// NewWebsocketService creates a new WebSocket service
func NewWebsocketService(
	websocketRepo repository.WebsocketRepository,
	websocketPubSub *redis.WebsocketPubSub,
) *WebSocketService {
	service := &WebSocketService{
		websocketRepo:   websocketRepo,
		websocketPubSub: websocketPubSub,
		connections:     make(map[string]*WebSocketConnection),
		userConnections: make(map[int][]string),
	}

	// If Redis PubSub is provided, subscribe to messages
	if websocketPubSub != nil {
		go service.subscribeToRedis()
	}

	// Start a goroutine to clean up stale connections
	go service.cleanupStaleConnections()

	return service
}

// RegisterConnection registers a new WebSocket connection
func (s *WebSocketService) RegisterConnection(ctx context.Context, userID int, clientInfo string) (*WebSocketConnection, error) {
	// Generate a unique connection ID
	connectionID := uuid.New().String()

	// Create a new connection
	conn := &WebSocketConnection{
		ID:       connectionID,
		UserID:   userID,
		Send:     make(chan []byte, 256),
		LastPing: time.Now(),
	}

	// Save connection in memory
	s.mu.Lock()
	s.connections[connectionID] = conn
	s.userConnections[userID] = append(s.userConnections[userID], connectionID)
	s.mu.Unlock()

	// Save connection to repository
	wsConn := &models.WebSocketConnection{
		ConnectionID: connectionID,
		UserID:       userID,
		ClientInfo:   clientInfo,
		ConnectedAt:  time.Now(),
		LastPingAt:   time.Now(),
		IsActive:     true,
	}

	if err := s.websocketRepo.SaveConnection(ctx, wsConn); err != nil {
		return nil, fmt.Errorf("failed to save WebSocket connection: %w", err)
	}

	return conn, nil
}

// UnregisterConnection removes a WebSocket connection
func (s *WebSocketService) UnregisterConnection(ctx context.Context, connectionID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Get the connection
	conn, exists := s.connections[connectionID]
	if !exists {
		return fmt.Errorf("connection not found: %s", connectionID)
	}

	// Remove from user connections
	userID := conn.UserID
	if conns, ok := s.userConnections[userID]; ok {
		var newConns []string
		for _, id := range conns {
			if id != connectionID {
				newConns = append(newConns, id)
			}
		}
		if len(newConns) > 0 {
			s.userConnections[userID] = newConns
		} else {
			delete(s.userConnections, userID)
		}
	}

	// Remove from connections
	delete(s.connections, connectionID)

	// Close the send channel
	close(conn.Send)

	// Delete connection from repository
	if err := s.websocketRepo.DeleteConnection(ctx, connectionID); err != nil {
		return fmt.Errorf("failed to delete WebSocket connection: %w", err)
	}

	return nil
}

// SendMessageToUser sends a WebSocket message to all connections of a specific user
func (s *WebSocketService) SendMessageToUser(userID int, message WebsocketMessage) {
	// If using Redis, publish the message
	if s.websocketPubSub != nil {
		s.websocketPubSub.PublishToUser(userID, message)
		return
	}

	// Otherwise, send directly to local connections
	s.sendMessageToUserLocal(userID, message)
}

// SendMessageToTopic sends a WebSocket message to all connections subscribed to a specific topic
func (s *WebSocketService) SendMessageToTopic(topic string, message WebsocketMessage) {
	// If using Redis, publish the message
	if s.websocketPubSub != nil {
		s.websocketPubSub.PublishToTopic(topic, message)
		return
	}

	// Otherwise, send directly to local connections
	// Note: Topic subscription would need to be implemented
}

// UpdateLastPing updates the last ping time for a connection
func (s *WebSocketService) UpdateLastPing(connectionID string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if conn, exists := s.connections[connectionID]; exists {
		conn.LastPing = time.Now()
	}
}

// subscribeToRedis subscribes to Redis channels for distributed messaging
func (s *WebSocketService) subscribeToRedis() {
	if s.websocketPubSub == nil {
		return
	}

	userCh := s.websocketPubSub.SubscribeToUsers()
	for message := range userCh {
		userID := message.UserID
		// Type assertion to convert message.Message to WebsocketMessage
		if wsMsg, ok := message.Message.(WebsocketMessage); ok {
			s.sendMessageToUserLocal(userID, wsMsg)
		} else {
			// If message is not of expected type, try marshal/unmarshal approach
			var wsMsg WebsocketMessage
			msgBytes, err := json.Marshal(message.Message)
			if err == nil {
				if err = json.Unmarshal(msgBytes, &wsMsg); err == nil {
					s.sendMessageToUserLocal(userID, wsMsg)
				}
			}
		}
	}
}

// sendMessageToUserLocal sends a message to all local connections for a user
func (s *WebSocketService) sendMessageToUserLocal(userID int, message WebsocketMessage) {
	s.mu.RLock()
	connectionIDs, exists := s.userConnections[userID]
	s.mu.RUnlock()

	if !exists {
		return
	}

	// Marshal the message once
	msgBytes, err := json.Marshal(message)
	if err != nil {
		fmt.Printf("Error marshaling WebSocket message: %v\n", err)
		return
	}

	// Send to all connections of the user
	for _, connectionID := range connectionIDs {
		s.mu.RLock()
		conn, exists := s.connections[connectionID]
		s.mu.RUnlock()

		if exists {
			select {
			case conn.Send <- msgBytes:
				// Message sent successfully
			default:
				// Channel full or closed, remove the connection
				go func(connID string) {
					ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
					defer cancel()
					s.UnregisterConnection(ctx, connID)
				}(connectionID)
			}
		}
	}
}

// cleanupStaleConnections periodically cleans up stale connections
func (s *WebSocketService) cleanupStaleConnections() {
	ticker := time.NewTicker(15 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
		// Clean up in repository - connections older than 1 hour without ping
		if err := s.websocketRepo.CleanupStaleConnections(ctx, 60); err != nil {
			fmt.Printf("Error cleaning up stale connections: %v\n", err)
		}

		// Clean up in memory
		s.mu.Lock()
		staleTime := time.Now().Add(-30 * time.Minute)
		for id, conn := range s.connections {
			if conn.LastPing.Before(staleTime) {
				// Close the send channel
				close(conn.Send)

				// Remove from user connections
				userID := conn.UserID
				if conns, ok := s.userConnections[userID]; ok {
					var newConns []string
					for _, connID := range conns {
						if connID != id {
							newConns = append(newConns, connID)
						}
					}
					if len(newConns) > 0 {
						s.userConnections[userID] = newConns
					} else {
						delete(s.userConnections, userID)
					}
				}

				// Remove from connections
				delete(s.connections, id)
			}
		}
		s.mu.Unlock()
		cancel()
	}
}

// SendToUser sends a message to a specific user with the given topic and payload
func (s *WebSocketService) SendToUser(ctx context.Context, userID int, topic string, payload string) error {
	message := WebsocketMessage{
		Type:    "notification",
		Topic:   topic,
		Payload: payload,
	}

	s.SendMessageToUser(userID, message)
	return nil
}
