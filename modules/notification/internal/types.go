package internal

import (
	"context"
	"time"
)

// NotificationConfig chứa cấu hình notification service
type NotificationConfig struct {
	EmailHost     string        `yaml:"email_host" env:"EMAIL_HOST"`
	EmailPort     int           `yaml:"email_port" env:"EMAIL_PORT" envDefault:"587"`
	EmailUsername string        `yaml:"email_username" env:"EMAIL_USERNAME"`
	EmailPassword string        `yaml:"email_password" env:"EMAIL_PASSWORD"`
	EmailFrom     string        `yaml:"email_from" env:"EMAIL_FROM"`
	SMSProvider   string        `yaml:"sms_provider" env:"SMS_PROVIDER" envDefault:"twilio"`
	SMSEnabled    bool          `yaml:"sms_enabled" env:"SMS_ENABLED" envDefault:"false"`
	PushEnabled   bool          `yaml:"push_enabled" env:"PUSH_ENABLED" envDefault:"false"`
	QueueEnabled  bool          `yaml:"queue_enabled" env:"QUEUE_ENABLED" envDefault:"false"`
	CacheEnabled  bool          `yaml:"cache_enabled" env:"CACHE_ENABLED" envDefault:"false"`
	RedisAddr     string        `yaml:"redis_addr" env:"REDIS_ADDR" envDefault:"localhost:6379"`
	CacheTTL      int           `yaml:"cache_ttl" env:"CACHE_TTL" envDefault:"300"`
	RetryAttempts int           `yaml:"retry_attempts" env:"RETRY_ATTEMPTS" envDefault:"3"`
	RetryDelay    time.Duration `yaml:"retry_delay" env:"RETRY_DELAY" envDefault:"5s"`
	Message       string        `env:"MESSAGE" envDefault:"Xin chào từ module Notification!"`
}

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrInvalidEmail là lỗi khi email không hợp lệ
	ErrInvalidEmail ServiceError = "invalid_email"
	// ErrInvalidPhone là lỗi khi số điện thoại không hợp lệ
	ErrInvalidPhone ServiceError = "invalid_phone"
	// ErrSendFailed là lỗi khi gửi thông báo thất bại
	ErrSendFailed ServiceError = "send_failed"
	// ErrInvalidTemplate là lỗi khi template không hợp lệ
	ErrInvalidTemplate ServiceError = "invalid_template"
	// ErrInvalidRecipient là lỗi khi người nhận không hợp lệ
	ErrInvalidRecipient ServiceError = "invalid_recipient"
)

func (e ServiceError) Error() string {
	return string(e)
}

// NotificationService định nghĩa interface cho notification service
type NotificationService interface {
	CreateNotification(ctx context.Context, notification interface{}) (int, error)
	GetNotification(ctx context.Context, notificationID int) (interface{}, error)
	GetUserNotifications(ctx context.Context, userID int, cursor string, limit int) ([]interface{}, string, error)
	MarkAsRead(ctx context.Context, notificationID int) error
	MarkAllAsRead(ctx context.Context, userID int) error
	GetUnreadCount(ctx context.Context, userID int) (int, error)
	DeleteNotification(ctx context.Context, notificationID int) error
}

// NotificationInfo chứa thông tin về thông báo đã gửi
type NotificationInfo struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`
	Recipient     string                 `json:"recipient"`
	Subject       string                 `json:"subject,omitempty"`
	Content       string                 `json:"content"`
	Status        string                 `json:"status"`
	SentAt        time.Time              `json:"sent_at"`
	DeliveredAt   *time.Time             `json:"delivered_at,omitempty"`
	FailureReason string                 `json:"failure_reason,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}
