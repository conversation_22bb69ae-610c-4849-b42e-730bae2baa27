package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/notification/models"
	"github.com/webnew/wn-backend-v2/modules/notification/repository"
)

// TemplateRepository implements the repository interface for templates
type TemplateRepository struct {
	db *sqlx.DB
}

// NewTemplateRepository creates a new MySQL template repository
func NewTemplateRepository(db *sqlx.DB) repository.TemplateRepository {
	return &TemplateRepository{
		db: db,
	}
}

// Create inserts a new template into the database
func (r *TemplateRepository) Create(ctx context.Context, template *models.Template) (int, error) {
	query := `
		INSERT INTO notification_templates (
			template_code, title_template, content_template, notification_type, 
			is_active, created_at, updated_at
		) VALUES (
			:template_code, :title_template, :content_template, :notification_type, 
			:is_active, :created_at, :updated_at
		)
	`

	template.CreatedAt = time.Now()
	template.UpdatedAt = time.Now()

	result, err := r.db.NamedExecContext(
		ctx,
		query,
		template,
	)
	if err != nil {
		return 0, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	return int(id), nil
}

// GetByID retrieves a template by ID
func (r *TemplateRepository) GetByID(ctx context.Context, templateID int) (*models.Template, error) {
	query := `
		SELECT * FROM notification_templates
		WHERE template_id = ?
	`

	var template models.Template
	err := r.db.GetContext(ctx, &template, query, templateID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &template, nil
}

// GetByCode retrieves a template by code
func (r *TemplateRepository) GetByCode(ctx context.Context, templateCode string) (*models.Template, error) {
	query := `
		SELECT * FROM notification_templates
		WHERE template_code = ?
	`

	log.Printf("[DEBUG] Tìm kiếm template với code: %s", templateCode)

	var template models.Template
	err := r.db.GetContext(ctx, &template, query, templateCode)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("[DEBUG] Không tìm thấy template với code: %s", templateCode)
			return nil, nil
		}
		log.Printf("[DEBUG] Lỗi khi lấy template: %v", err)
		return nil, err
	}

	log.Printf("[DEBUG] Đã tìm thấy template: ID=%d, Code=%s", template.TemplateID, template.TemplateCode)
	log.Printf("[DEBUG] Title template: %s", template.TitleTemplate)
	log.Printf("[DEBUG] Content template: %s", template.ContentTemplate)

	return &template, nil
}

// List retrieves templates with filtering and pagination
func (r *TemplateRepository) List(
	ctx context.Context,
	cursor string,
	limit int,
	filters map[string]interface{},
) ([]*models.Template, string, error) {
	baseQuery := `
		SELECT * FROM notification_templates
		WHERE 1=1
	`

	if limit <= 0 {
		limit = 20
	}

	var args []interface{}
	whereConditions := []string{}

	// Apply filters
	if notificationType, ok := filters["notification_type"]; ok {
		whereConditions = append(whereConditions, "notification_type = ?")
		args = append(args, notificationType)
	}

	if isActive, ok := filters["is_active"]; ok {
		whereConditions = append(whereConditions, "is_active = ?")
		args = append(args, isActive)
	}

	if cursor != "" {
		whereConditions = append(whereConditions, "template_id < ?")
		cursorID, err := decodeTemplateCursor(cursor)
		if err != nil {
			return nil, "", err
		}
		args = append(args, cursorID)
	}

	// Append WHERE conditions
	for _, condition := range whereConditions {
		baseQuery += " AND " + condition
	}

	// Apply sort
	baseQuery += " ORDER BY template_id DESC LIMIT ?"
	args = append(args, limit+1) // +1 to check if there are more results

	// Execute query
	rows, err := r.db.QueryxContext(ctx, baseQuery, args...)
	if err != nil {
		return nil, "", err
	}
	defer rows.Close()

	templates := []*models.Template{}
	hasMore := false
	var lastID int

	count := 0
	for rows.Next() {
		var template models.Template
		if err := rows.StructScan(&template); err != nil {
			return nil, "", err
		}

		count++
		if count <= limit {
			templates = append(templates, &template)
			lastID = template.TemplateID
		} else {
			hasMore = true
			break
		}
	}

	if err := rows.Err(); err != nil {
		return nil, "", err
	}

	var nextCursor string
	if hasMore && len(templates) > 0 {
		nextCursor = encodeTemplateCursor(lastID)
	}

	return templates, nextCursor, nil
}

// Update updates a template
func (r *TemplateRepository) Update(ctx context.Context, template *models.Template) error {
	query := `
		UPDATE notification_templates SET
			template_code = :template_code,
			title_template = :title_template,
			content_template = :content_template,
			notification_type = :notification_type,
			is_active = :is_active,
			updated_at = :updated_at
		WHERE template_id = :template_id
	`

	template.UpdatedAt = time.Now()

	_, err := r.db.NamedExecContext(ctx, query, template)
	return err
}

// Delete deletes a template
func (r *TemplateRepository) Delete(ctx context.Context, templateID int) error {
	query := `DELETE FROM notification_templates WHERE template_id = ?`
	_, err := r.db.ExecContext(ctx, query, templateID)
	return err
}

// Helper functions for cursor pagination
func encodeTemplateCursor(id int) string {
	return fmt.Sprintf("%d", id)
}

func decodeTemplateCursor(cursor string) (int, error) {
	var id int
	_, err := fmt.Sscanf(cursor, "%d", &id)
	return id, err
}
