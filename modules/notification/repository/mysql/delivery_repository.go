package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/notification/models"
)

// DeliveryRepository implements the DeliveryRepository interface
type DeliveryRepository struct {
	db *sqlx.DB
}

// NewDeliveryRepository creates a new delivery repository
func NewDeliveryRepository(db *sqlx.DB) *DeliveryRepository {
	return &DeliveryRepository{
		db: db,
	}
}

// CreateDelivery adds a new notification delivery to the queue
func (r *DeliveryRepository) CreateDelivery(ctx context.Context, delivery *models.Delivery) (int, error) {
	query := `
		INSERT INTO notification_queue (
			notification_id, channel_code, status, retry_count,
			next_retry_at, last_error
		) VALUES (
			:notification_id, :channel_code, :status, :retry_count,
			:next_retry_at, :last_error
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, delivery)
	if err != nil {
		return 0, fmt.Errorf("failed to create delivery: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get last insert ID: %w", err)
	}

	return int(id), nil
}

// GetPendingDeliveries retrieves pending deliveries to be processed
func (r *DeliveryRepository) GetPendingDeliveries(ctx context.Context, limit int) ([]*models.Delivery, error) {
	if limit <= 0 {
		limit = 50
	}

	query := `
		SELECT * FROM notification_queue 
		WHERE status = 'pending' AND (next_retry_at IS NULL OR next_retry_at <= ?)
		ORDER BY next_retry_at ASC, queue_id ASC
		LIMIT ?
	`

	var deliveries []*models.Delivery
	err := r.db.SelectContext(ctx, &deliveries, query, time.Now(), limit)
	if err != nil {
		if err == sql.ErrNoRows {
			return []*models.Delivery{}, nil
		}
		return nil, fmt.Errorf("failed to get pending deliveries: %w", err)
	}

	return deliveries, nil
}

// UpdateDeliveryStatus updates the status of a delivery
func (r *DeliveryRepository) UpdateDeliveryStatus(ctx context.Context, queueID int, status string, errorMsg string) error {
	var query string
	var args []interface{}

	switch status {
	case "processing":
		query = `
			UPDATE notification_queue
			SET status = ?, updated_at = NOW()
			WHERE queue_id = ?
		`
		args = []interface{}{status, queueID}
	case "sent":
		query = `
			UPDATE notification_queue
			SET status = ?, updated_at = NOW()
			WHERE queue_id = ?
		`
		args = []interface{}{status, queueID}
	case "failed":
		query = `
			UPDATE notification_queue
			SET status = ?, retry_count = retry_count + 1, last_error = ?,
				next_retry_at = CASE 
					WHEN retry_count < 3 THEN DATE_ADD(NOW(), INTERVAL (POW(2, retry_count)) MINUTE)
					ELSE NULL
				END,
				updated_at = NOW()
			WHERE queue_id = ?
		`
		args = []interface{}{status, errorMsg, queueID}
	default:
		return fmt.Errorf("invalid status: %s", status)
	}

	_, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("failed to update delivery status: %w", err)
	}

	return nil
}

// LogDeliveryAttempt logs an attempt to deliver a notification
func (r *DeliveryRepository) LogDeliveryAttempt(ctx context.Context, log *models.DeliveryLog) error {
	query := `
		INSERT INTO notification_logs (
			notification_id, channel_code, status, error_message, metadata
		) VALUES (
			:notification_id, :channel_code, :status, :error_message, :metadata
		)
	`

	_, err := r.db.NamedExecContext(ctx, query, log)
	if err != nil {
		return fmt.Errorf("failed to log delivery attempt: %w", err)
	}

	return nil
}

// GetDeliveryLogs retrieves delivery logs for a notification
func (r *DeliveryRepository) GetDeliveryLogs(ctx context.Context, notificationID int) ([]*models.DeliveryLog, error) {
	query := `
		SELECT * FROM notification_logs
		WHERE notification_id = ?
		ORDER BY created_at DESC
	`

	var logs []*models.DeliveryLog
	err := r.db.SelectContext(ctx, &logs, query, notificationID)
	if err != nil {
		if err == sql.ErrNoRows {
			return []*models.DeliveryLog{}, nil
		}
		return nil, fmt.Errorf("failed to get delivery logs: %w", err)
	}

	return logs, nil
}
