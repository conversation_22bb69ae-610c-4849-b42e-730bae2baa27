package mysql

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/notification/models"
)

// PreferenceRepository implements the PreferenceRepository interface
type PreferenceRepository struct {
	db *sqlx.DB
}

// NewPreferenceRepository creates a new preference repository
func NewPreferenceRepository(db *sqlx.DB) *PreferenceRepository {
	return &PreferenceRepository{
		db: db,
	}
}

// C<PERSON> adds a new notification preference to the database
func (r *PreferenceRepository) Create(ctx context.Context, preference *models.Preference) (int, error) {
	query := `
		INSERT INTO notification_user_preferences (
			user_id, notification_type, channel_code, is_enabled
		) VALUES (
			:user_id, :notification_type, :channel_code, :is_enabled
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, preference)
	if err != nil {
		return 0, fmt.<PERSON>rrorf("failed to create preference: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get last insert ID: %w", err)
	}

	return int(id), nil
}

// GetByID retrieves a preference by its ID
func (r *PreferenceRepository) GetByID(ctx context.Context, preferenceID int) (*models.Preference, error) {
	query := `SELECT * FROM notification_user_preferences WHERE preference_id = ?`

	var preference models.Preference
	err := r.db.GetContext(ctx, &preference, query, preferenceID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get preference by ID: %w", err)
	}

	return &preference, nil
}

// GetUserPreferences retrieves preferences for a specific user with pagination
func (r *PreferenceRepository) GetUserPreferences(ctx context.Context, userID int, cursor string, limit int, filters map[string]interface{}) ([]*models.Preference, string, error) {
	if limit <= 0 {
		limit = 20
	}

	query := `SELECT * FROM notification_user_preferences WHERE user_id = ?`
	args := []interface{}{userID}

	// Add filters
	if filters != nil {
		if val, ok := filters["is_enabled"]; ok {
			query += " AND is_enabled = ?"
			args = append(args, val)
		}
		if val, ok := filters["notification_type"]; ok {
			query += " AND notification_type = ?"
			args = append(args, val)
		}
		if val, ok := filters["channel_code"]; ok {
			query += " AND channel_code = ?"
			args = append(args, val)
		}
	}

	// Add cursor pagination
	if cursor != "" {
		query += " AND preference_id < ?"
		args = append(args, cursor)
	}

	query += " ORDER BY preference_id DESC LIMIT ?"
	args = append(args, limit+1) // Get one extra to determine if there are more

	var preferences []*models.Preference
	err := r.db.SelectContext(ctx, &preferences, query, args...)
	if err != nil {
		return nil, "", fmt.Errorf("failed to get user preferences: %w", err)
	}

	var nextCursor string
	if len(preferences) > limit {
		lastPreference := preferences[limit]
		nextCursor = fmt.Sprintf("%d", lastPreference.PreferenceID)
		preferences = preferences[:limit]
	}

	return preferences, nextCursor, nil
}

// Update updates a preference
func (r *PreferenceRepository) Update(ctx context.Context, preference *models.Preference) error {
	query := `
		UPDATE notification_user_preferences 
		SET 
			user_id = :user_id, 
			notification_type = :notification_type, 
			channel_code = :channel_code,
			is_enabled = :is_enabled,
			updated_at = NOW()
		WHERE preference_id = :preference_id
	`

	_, err := r.db.NamedExecContext(ctx, query, preference)
	if err != nil {
		return fmt.Errorf("failed to update preference: %w", err)
	}

	return nil
}

// Delete deletes a preference
func (r *PreferenceRepository) Delete(ctx context.Context, preferenceID int) error {
	query := `DELETE FROM notification_user_preferences WHERE preference_id = ?`

	_, err := r.db.ExecContext(ctx, query, preferenceID)
	if err != nil {
		return fmt.Errorf("failed to delete preference: %w", err)
	}

	return nil
}

// SetDefaultPreferences sets default notification preferences for a user
func (r *PreferenceRepository) SetDefaultPreferences(ctx context.Context, userID int) error {
	// Define the default notification types and channels
	defaultPreferences := []struct {
		NotificationType string
		ChannelCode      string
		IsEnabled        bool
	}{
		{"system", "in_app", true},
		{"account", "in_app", true},
		{"content", "in_app", true},
		{"account", "email", true},
		{"system", "email", true},
	}

	// Begin transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	// Rollback on panic
	defer func() {
		if r := recover(); r != nil {
			_ = tx.Rollback()
		}
	}()

	// Insert default preferences
	query := `
		INSERT INTO notification_user_preferences (
			user_id, notification_type, channel_code, is_enabled
		) VALUES (?, ?, ?, ?)
	`

	for _, p := range defaultPreferences {
		_, err := tx.ExecContext(ctx, query, userID, p.NotificationType, p.ChannelCode, p.IsEnabled)
		if err != nil {
			_ = tx.Rollback()
			return fmt.Errorf("failed to insert default preference: %w", err)
		}
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}
