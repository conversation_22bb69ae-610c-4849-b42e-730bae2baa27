package mysql

import (
	"context"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/notification/models"
)

// WebsocketRepository implements the WebsocketRepository interface
type WebsocketRepository struct {
	db *sqlx.DB
}

// NewWebsocketRepository creates a new websocket repository
func NewWebsocketRepository(db *sqlx.DB) *WebsocketRepository {
	return &WebsocketRepository{
		db: db,
	}
}

// SaveConnection saves a websocket connection to the database
func (r *WebsocketRepository) SaveConnection(ctx context.Context, connection *models.WebSocketConnection) error {
	query := `
		INSERT INTO notification_websocket_connections (
			connection_id, user_id, client_info, connected_at, last_ping_at, is_active
		) VALUES (
			:connection_id, :user_id, :client_info, :connected_at, :last_ping_at, :is_active
		)
		ON DUPLICATE KEY UPDATE
			last_ping_at = VALUES(last_ping_at),
			is_active = VALUES(is_active)
	`

	_, err := r.db.NamedExecContext(ctx, query, connection)
	if err != nil {
		return fmt.Errorf("failed to save websocket connection: %w", err)
	}

	return nil
}

// GetUserConnections retrieves active websocket connections for a user
func (r *WebsocketRepository) GetUserConnections(ctx context.Context, userID int) ([]*models.WebSocketConnection, error) {
	query := `
		SELECT * FROM notification_websocket_connections
		WHERE user_id = ? AND is_active = true
		ORDER BY last_ping_at DESC
	`

	var connections []*models.WebSocketConnection
	err := r.db.SelectContext(ctx, &connections, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user connections: %w", err)
	}

	return connections, nil
}

// DeleteConnection deletes a websocket connection
func (r *WebsocketRepository) DeleteConnection(ctx context.Context, connectionID string) error {
	query := `
		UPDATE notification_websocket_connections
		SET is_active = false
		WHERE connection_id = ?
	`

	_, err := r.db.ExecContext(ctx, query, connectionID)
	if err != nil {
		return fmt.Errorf("failed to delete websocket connection: %w", err)
	}

	return nil
}

// CleanupStaleConnections removes stale websocket connections
func (r *WebsocketRepository) CleanupStaleConnections(ctx context.Context, olderThan int) error {
	cutoffTime := time.Now().Add(-time.Duration(olderThan) * time.Minute)

	query := `
		UPDATE notification_websocket_connections
		SET is_active = false
		WHERE last_ping_at < ? AND is_active = true
	`

	_, err := r.db.ExecContext(ctx, query, cutoffTime)
	if err != nil {
		return fmt.Errorf("failed to cleanup stale connections: %w", err)
	}

	// Hard delete very old connections
	deleteQuery := `
		DELETE FROM notification_websocket_connections
		WHERE last_ping_at < ? AND is_active = false
	`

	veryOldCutoff := time.Now().Add(-time.Duration(olderThan*24) * time.Hour)
	_, err = r.db.ExecContext(ctx, deleteQuery, veryOldCutoff)
	if err != nil {
		return fmt.Errorf("failed to delete old connections: %w", err)
	}

	return nil
}
