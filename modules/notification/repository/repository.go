// Repository interfaces for the notification module
package repository

import (
	"context"

	"wnapi/modules/notification/models"
)

// NotificationRepository defines the interface for notification data operations
type NotificationRepository interface {
	Create(ctx context.Context, notification *models.Notification) (int, error)
	GetByID(ctx context.Context, notificationID int) (*models.Notification, error)
	GetByUserID(ctx context.Context, userID int, cursor string, limit int, filters map[string]interface{}) ([]*models.Notification, string, error)
	MarkAsRead(ctx context.Context, notificationID int) error
	MarkAllAsRead(ctx context.Context, userID int) error
	GetUnreadCount(ctx context.Context, userID int) (int, error)
	Update(ctx context.Context, notification *models.Notification) error
	Delete(ctx context.Context, notificationID int) error
}

// ChannelRepository defines the interface for notification channel data operations
type ChannelRepository interface {
	Create(ctx context.Context, channel *models.Channel) (int, error)
	GetByID(ctx context.Context, channelID int) (*models.Channel, error)
	GetByCode(ctx context.Context, code string) (*models.Channel, error)
	List(ctx context.Context, cursor string, limit int, filters map[string]interface{}) ([]*models.Channel, string, error)
	Update(ctx context.Context, channel *models.Channel) error
	Delete(ctx context.Context, channelID int) error
}

// TemplateRepository defines the interface for notification template data operations
type TemplateRepository interface {
	Create(ctx context.Context, template *models.Template) (int, error)
	GetByID(ctx context.Context, templateID int) (*models.Template, error)
	GetByCode(ctx context.Context, code string) (*models.Template, error)
	List(ctx context.Context, cursor string, limit int, filters map[string]interface{}) ([]*models.Template, string, error)
	Update(ctx context.Context, template *models.Template) error
	Delete(ctx context.Context, templateID int) error
}

// PreferenceRepository defines the interface for notification preference data operations
type PreferenceRepository interface {
	Create(ctx context.Context, preference *models.Preference) (int, error)
	GetByID(ctx context.Context, preferenceID int) (*models.Preference, error)
	GetUserPreferences(ctx context.Context, userID int, cursor string, limit int, filters map[string]interface{}) ([]*models.Preference, string, error)
	Update(ctx context.Context, preference *models.Preference) error
	Delete(ctx context.Context, preferenceID int) error
	SetDefaultPreferences(ctx context.Context, userID int) error
}

// DeliveryRepository defines the interface for notification delivery data operations
type DeliveryRepository interface {
	CreateDelivery(ctx context.Context, delivery *models.Delivery) (int, error)
	GetPendingDeliveries(ctx context.Context, limit int) ([]*models.Delivery, error)
	UpdateDeliveryStatus(ctx context.Context, queueID int, status string, errorMsg string) error
	LogDeliveryAttempt(ctx context.Context, log *models.DeliveryLog) error
	GetDeliveryLogs(ctx context.Context, notificationID int) ([]*models.DeliveryLog, error)
}

// WebsocketRepository defines the interface for WebSocket connection data operations
type WebsocketRepository interface {
	SaveConnection(ctx context.Context, connection *models.WebSocketConnection) error
	GetUserConnections(ctx context.Context, userID int) ([]*models.WebSocketConnection, error)
	DeleteConnection(ctx context.Context, connectionID string) error
	CleanupStaleConnections(ctx context.Context, olderThan int) error
}
