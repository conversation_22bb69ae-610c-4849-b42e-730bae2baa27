package handlers

import (
	"fmt"
	"net/http"

	"wnapi/modules/notification/dto"
	"wnapi/modules/notification/service"

	"github.com/gin-gonic/gin"
)

// SimpleNotificationHandler handles HTTP requests for notifications
type SimpleNotificationHandler struct {
	service service.NotificationService
}

// NewSimpleNotificationHandler creates a new notification handler
func NewSimpleNotificationHandler(service service.NotificationService) *SimpleNotificationHandler {
	return &SimpleNotificationHandler{
		service: service,
	}
}

// SendEmail handles email sending requests
func (h *SimpleNotificationHandler) SendEmail(c *gin.Context) {
	var req dto.EmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// Validate required fields
	if len(req.To) == 0 || req.Subject == "" {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Missing required fields: to and subject are required",
		})
		return
	}

	response, err := h.service.SendEmail(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to send email",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Email sent successfully",
		"data":    response,
	})
}

// SendBulkEmail handles bulk email sending requests
func (h *SimpleNotificationHandler) SendBulkEmail(c *gin.Context) {
	var req struct {
		Emails []dto.EmailRequest `json:"emails" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	var responses []dto.NotificationResponse
	var errors []string

	for _, email := range req.Emails {
		response, err := h.service.SendEmail(c.Request.Context(), email)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to send email to %v: %s", email.To, err.Error()))
			continue
		}
		responses = append(responses, *response)
	}

	if len(errors) > 0 {
		c.JSON(http.StatusPartialContent, gin.H{
			"success": false,
			"message": "Some emails failed to send",
			"data":    responses,
			"errors":  errors,
			"sent":    len(responses),
			"failed":  len(errors),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "All emails sent successfully",
		"data":    responses,
		"sent":    len(responses),
	})
}

// SendSMS handles SMS sending requests
func (h *SimpleNotificationHandler) SendSMS(c *gin.Context) {
	var req dto.SMSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// Validate required fields
	if len(req.To) == 0 || req.Message == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Missing required fields: to and message are required",
		})
		return
	}

	response, err := h.service.SendSMS(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to send SMS",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "SMS sent successfully",
		"data":    response,
	})
}

// SendBulkSMS handles bulk SMS sending requests
func (h *SimpleNotificationHandler) SendBulkSMS(c *gin.Context) {
	var req struct {
		SMSList []dto.SMSRequest `json:"sms_list" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	var responses []dto.NotificationResponse
	var errors []string

	for _, sms := range req.SMSList {
		response, err := h.service.SendSMS(c.Request.Context(), sms)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to send SMS to %v: %s", sms.To, err.Error()))
			continue
		}
		responses = append(responses, *response)
	}

	if len(errors) > 0 {
		c.JSON(http.StatusPartialContent, gin.H{
			"success": false,
			"message": "Some SMS failed to send",
			"data":    responses,
			"errors":  errors,
			"sent":    len(responses),
			"failed":  len(errors),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "All SMS sent successfully",
		"data":    responses,
		"sent":    len(responses),
	})
}

// SendPush handles push notification requests
func (h *SimpleNotificationHandler) SendPush(c *gin.Context) {
	var req dto.PushRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// Validate required fields
	if (len(req.To) == 0 && req.UserID == "") || req.Title == "" || req.Body == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Missing required fields: (to or userID), title, and body are required",
		})
		return
	}

	response, err := h.service.SendPushNotification(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to send push notification",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Push notification sent successfully",
		"data":    response,
	})
}

// SendBulkPush handles bulk push notification requests
func (h *SimpleNotificationHandler) SendBulkPush(c *gin.Context) {
	var req struct {
		PushList []dto.PushRequest `json:"push_list" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	var responses []dto.NotificationResponse
	var errors []string

	for _, push := range req.PushList {
		response, err := h.service.SendPushNotification(c.Request.Context(), push)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to send push notification to %v: %s", push.To, err.Error()))
			continue
		}
		responses = append(responses, *response)
	}

	if len(errors) > 0 {
		c.JSON(http.StatusPartialContent, gin.H{
			"success": false,
			"message": "Some push notifications failed to send",
			"data":    responses,
			"errors":  errors,
			"sent":    len(responses),
			"failed":  len(errors),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "All push notifications sent successfully",
		"data":    responses,
		"sent":    len(responses),
	})
}

// GetHistory retrieves notification history for a user
func (h *SimpleNotificationHandler) GetHistory(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "User ID is required",
		})
		return
	}

	history, err := h.service.GetNotificationHistory(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to retrieve notification history",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Notification history retrieved successfully",
		"data":    history,
		"count":   len(history),
	})
}

// GetStats retrieves notification statistics for a user
func (h *SimpleNotificationHandler) GetStats(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "User ID is required",
		})
		return
	}

	// Get notification history to calculate stats
	history, err := h.service.GetNotificationHistory(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to retrieve notification statistics",
			"error":   err.Error(),
		})
		return
	}

	// Calculate statistics
	stats := map[string]interface{}{
		"total": len(history),
		"by_type": map[string]int{
			"email": 0,
			"sms":   0,
			"push":  0,
		},
		"by_status": map[string]int{
			"sent":    0,
			"pending": 0,
			"failed":  0,
		},
	}

	typeStats := stats["by_type"].(map[string]int)
	statusStats := stats["by_status"].(map[string]int)

	for _, notification := range history {
		// Count by type
		if count, exists := typeStats[notification.Type]; exists {
			typeStats[notification.Type] = count + 1
		} else {
			typeStats[notification.Type] = 1
		}

		// Count by status
		if count, exists := statusStats[notification.Status]; exists {
			statusStats[notification.Status] = count + 1
		} else {
			statusStats[notification.Status] = 1
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Notification statistics retrieved successfully",
		"data":    stats,
	})
}
