package handlers

import (
	"strconv"
	"time"

	"wnapi/internal/pkg/response"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/models"

	"github.com/gin-gonic/gin"
)

// SimpleNotificationHandler xử lý HTTP request cho thông báo
type SimpleNotificationHandler struct {
	service internal.NotificationService
}

// NewSimpleNotificationHandler tạo handler mới
func NewSimpleNotificationHandler(service internal.NotificationService) *SimpleNotificationHandler {
	return &SimpleNotificationHandler{
		service: service,
	}
}

// GetNotifications lấy danh sách thông báo của người dùng
func (h *SimpleNotificationHandler) GetNotifications(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		handleInvalidUserID(c)
		return
	}

	cursor := c.<PERSON><PERSON>("cursor")
	limitStr := c.<PERSON><PERSON>ult<PERSON><PERSON><PERSON>("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	notifications, nextCursor, err := h.service.GetUserNotifications(c.Request.Context(), userID, cursor, limit)
	if err != nil {
		handleServerError(c, "Failed to get notifications")
		return
	}

	meta := map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    nextCursor != "",
	}

	response.Success(c, notifications, meta)
}

// GetNotification lấy chi tiết một thông báo
func (h *SimpleNotificationHandler) GetNotification(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handleInvalidNotificationID(c)
		return
	}

	notification, err := h.service.GetNotification(c.Request.Context(), id)
	if err != nil {
		handleServerError(c, "Failed to get notification")
		return
	}

	if notification == nil {
		handleNotificationNotFound(c)
		return
	}

	response.Success(c, notification, nil)
}

// MarkAsRead đánh dấu thông báo đã đọc
func (h *SimpleNotificationHandler) MarkAsRead(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handleInvalidNotificationID(c)
		return
	}

	err = h.service.MarkAsRead(c.Request.Context(), id)
	if err != nil {
		handleServerError(c, "Failed to mark notification as read")
		return
	}

	response.Success(c, gin.H{"success": true}, nil)
}

// MarkAllAsRead đánh dấu tất cả thông báo của người dùng đã đọc
func (h *SimpleNotificationHandler) MarkAllAsRead(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		handleInvalidUserID(c)
		return
	}

	err = h.service.MarkAllAsRead(c.Request.Context(), userID)
	if err != nil {
		handleServerError(c, "Failed to mark all notifications as read")
		return
	}

	response.Success(c, gin.H{"success": true}, nil)
}

// GetUnreadCount lấy số lượng thông báo chưa đọc
func (h *SimpleNotificationHandler) GetUnreadCount(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.Atoi(userIDStr)
	if err != nil {
		handleInvalidUserID(c)
		return
	}

	count, err := h.service.GetUnreadCount(c.Request.Context(), userID)
	if err != nil {
		handleServerError(c, "Failed to get unread count")
		return
	}

	response.Success(c, gin.H{"count": count}, nil)
}

// CreateNotification tạo thông báo mới
func (h *SimpleNotificationHandler) CreateNotification(c *gin.Context) {
	var notification models.Notification
	if err := c.ShouldBindJSON(&notification); err != nil {
		response.BadRequest(c, "Invalid request body", "INVALID_REQUEST", []interface{}{
			map[string]string{"error": err.Error()},
		})
		return
	}

	// Đặt các trường bắt buộc
	notification.CreatedAt = time.Now()
	notification.UpdatedAt = time.Now()
	notification.IsRead = false
	notification.IsSent = false

	id, err := h.service.CreateNotification(c.Request.Context(), &notification)
	if err != nil {
		handleServerError(c, "Failed to create notification")
		return
	}

	notification.NotificationID = uint(id)
	response.Success(c, notification, nil)
}

// DeleteNotification xóa thông báo
func (h *SimpleNotificationHandler) DeleteNotification(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handleInvalidNotificationID(c)
		return
	}

	err = h.service.DeleteNotification(c.Request.Context(), id)
	if err != nil {
		handleServerError(c, "Failed to delete notification")
		return
	}

	response.Success(c, gin.H{"success": true}, nil)
}
