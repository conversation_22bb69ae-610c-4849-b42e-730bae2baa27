package api

import (
	"wnapi/internal/core"
	"wnapi/modules/notification/api/handlers"
	"wnapi/modules/notification/internal"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module notification
type Handler struct {
	notificationHandler *handlers.SimpleNotificationHandler
}

// NewHandler tạo một handler mới
func NewHandler(notificationService internal.NotificationService) *Handler {
	return &Handler{
		notificationHandler: handlers.NewSimpleNotificationHandler(notificationService),
	}
}

// RegisterRoutes đăng ký tất cả routes cho module notification
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/v1/notifications")

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)

	// Protected routes (có thể thêm JWT middleware sau)
	protectedRoutes := apiGroup.Group("")
	protectedRoutes.Use(func(c *gin.Context) {
		// TODO: Sử dụng middleware JWT từ server khi nó được định nghĩa
		// Tạm thời cho phép mọi request đi qua
		c.Next()
	})

	// Email API
	emailGroup := protectedRoutes.Group("/email")
	{
		emailGroup.POST("/send", h.notificationHandler.SendEmail)
		emailGroup.POST("/send-bulk", h.notificationHandler.SendBulkEmail)
	}

	// SMS API
	smsGroup := protectedRoutes.Group("/sms")
	{
		smsGroup.POST("/send", h.notificationHandler.SendSMS)
		smsGroup.POST("/send-bulk", h.notificationHandler.SendBulkSMS)
	}

	// Push Notification API
	pushGroup := protectedRoutes.Group("/push")
	{
		pushGroup.POST("/send", h.notificationHandler.SendPush)
		pushGroup.POST("/send-bulk", h.notificationHandler.SendBulkPush)
	}

	// History API
	historyGroup := protectedRoutes.Group("/history")
	{
		historyGroup.GET("/:user_id", h.notificationHandler.GetHistory)
		historyGroup.GET("/:user_id/stats", h.notificationHandler.GetStats)
	}

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status": "ok",
		"module": "notification",
	})
}
