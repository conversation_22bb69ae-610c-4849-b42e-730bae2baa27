package models

import (
	"time"
)

// Notification represents a notification in the system
type Notification struct {
	NotificationID   uint        `db:"notification_id" json:"notification_id" gorm:"primaryKey"`
	UserID           uint        `db:"user_id" json:"user_id"`
	Title            string     `db:"title" json:"title"`
	Content          string     `db:"content" json:"content"`
	NotificationType string     `db:"notification_type" json:"notification_type"`
	ReferenceType    string     `db:"reference_type" json:"reference_type"`
	ReferenceID      string     `db:"reference_id" json:"reference_id"`
	IsRead           bool       `db:"is_read" json:"is_read"`
	IsSent           bool       `db:"is_sent" json:"is_sent"`
	SentAt           *time.Time `db:"sent_at" json:"sent_at,omitempty"`
	CreatedAt        time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time  `db:"updated_at" json:"updated_at"`
}

// NotificationType constants
const (
	NotificationTypeInfo      = "info"
	NotificationTypeWarning   = "warning"
	NotificationTypeAlert     = "alert"
	NotificationTypePromotion = "promotion"
	NotificationTypeSystem    = "system"
)

// TableName returns the table name for the Notification model
func (Notification) TableName() string {
	return "notifications"
}
