package internal

import (
	"context"
	"net/http"
	"time"
	"wnapi/modules/auth/dto"
)

// AuthConfig chứa cấu hình auth service
type AuthConfig struct {
	JWTSecret          string        `yaml:"jwt_secret" env:"JWT_SECRET"`
	AccessTokenExpiry  time.Duration `yaml:"access_token_expiry" env:"ACCESS_TOKEN_EXPIRY" envDefault:"15m"`
	RefreshTokenExpiry time.Duration `yaml:"refresh_token_expiry" env:"REFRESH_TOKEN_EXPIRY" envDefault:"168h"`
	Message            string        `env:"MESSAGE" envDefault:"Xin chào từ module Auth!"`
}

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrInvalidCredentials là lỗi khi thông tin đăng nhập không hợp lệ
	ErrInvalidCredentials ServiceError = "invalid_credentials"
	// ErrUserAlreadyExists là lỗi khi user đã tồn tại
	ErrUserAlreadyExists ServiceError = "user_already_exists"
	// ErrInvalidToken là lỗi khi token không hợp lệ
	ErrInvalidToken ServiceError = "invalid_token"
	// ErrExpiredToken là lỗi khi token đã hết hạn
	ErrExpiredToken ServiceError = "expired_token"
	// ErrUserNotFound là lỗi khi không tìm thấy user
	ErrUserNotFound ServiceError = "user_not_found"
	// ErrTokenNotFound là lỗi khi không tìm thấy token
	ErrTokenNotFound ServiceError = "token_not_found"
	// ErrInvalidPassword là lỗi khi mật khẩu không hợp lệ
	ErrInvalidPassword ServiceError = "invalid_password"
	// ErrEmailNotVerified là lỗi khi email chưa được xác thực
	ErrEmailNotVerified ServiceError = "email_not_verified"
	// ErrInvalidUserType là lỗi khi loại người dùng không hợp lệ
	ErrInvalidUserType ServiceError = "invalid_user_type"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrInvalidCredentials: {
		StatusCode: http.StatusUnauthorized,
		Message:    "Thông tin đăng nhập không hợp lệ",
		ErrorCode:  "INVALID_CREDENTIALS",
	},
	ErrUserAlreadyExists: {
		StatusCode: http.StatusConflict,
		Message:    "Người dùng đã tồn tại",
		ErrorCode:  "USER_ALREADY_EXISTS",
	},
	ErrInvalidToken: {
		StatusCode: http.StatusUnauthorized,
		Message:    "Token không hợp lệ",
		ErrorCode:  "INVALID_TOKEN",
	},
	ErrExpiredToken: {
		StatusCode: http.StatusUnauthorized,
		Message:    "Token đã hết hạn",
		ErrorCode:  "EXPIRED_TOKEN",
	},
	ErrUserNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy người dùng",
		ErrorCode:  "USER_NOT_FOUND",
	},
	ErrTokenNotFound: {
		StatusCode: http.StatusUnauthorized,
		Message:    "Token không tồn tại hoặc đã hết hạn",
		ErrorCode:  "TOKEN_NOT_FOUND",
	},
	ErrInvalidPassword: {
		StatusCode: http.StatusBadRequest,
		Message:    "Mật khẩu không hợp lệ",
		ErrorCode:  "INVALID_PASSWORD",
	},
	ErrEmailNotVerified: {
		StatusCode: http.StatusForbidden,
		Message:    "Email chưa được xác thực",
		ErrorCode:  "EMAIL_NOT_VERIFIED",
	},
	ErrInvalidUserType: {
		StatusCode: http.StatusBadRequest,
		Message:    "Loại người dùng không hợp lệ",
		ErrorCode:  "INVALID_USER_TYPE",
	},
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}

	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// AuthService định nghĩa interface cho auth service
type AuthService interface {
	Register(ctx context.Context, req dto.RegisterRequest) (*UserInfo, error)
	Login(ctx context.Context, req dto.LoginRequest) (*dto.LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*dto.LoginResponse, error)
	ValidateToken(tokenString string) (map[string]interface{}, error)
}

// UserInfo chứa thông tin người dùng đã đăng ký
type UserInfo struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
}

// Repository định nghĩa interface cho authentication repository
// Được chuyển từ repository/repository.go để tránh import cycle

type Repository interface {
	// User
	CreateUser(ctx context.Context, user *User, password string) error
	GetUserByID(ctx context.Context, id int) (*User, error)
	GetUserByUsername(ctx context.Context, username string) (*User, error)
	GetUserByEmail(ctx context.Context, email string) (*User, error)
	UpdateUser(ctx context.Context, user *User) error
	DeleteUser(ctx context.Context, id int) error

	// Token
	CreateToken(ctx context.Context, token *Token) error
	GetTokenByValue(ctx context.Context, tokenValue string, tokenType TokenType) (*Token, error)
	DeleteToken(ctx context.Context, id string) error
	DeleteExpiredTokens(ctx context.Context) error
}

// User đại diện cho người dùng trong hệ thống
type User struct {
	ID           int       `db:"id" json:"id" gorm:"primaryKey"`
	Username     string    `db:"username" json:"username"`
	Email        string    `db:"email" json:"email"`
	PasswordHash string    `db:"password_hash" json:"-"`
	FullName     string    `db:"full_name" json:"full_name"`
	IsActive     bool      `db:"is_active" json:"is_active"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
}

// TokenType định nghĩa loại token
type TokenType string

const (
	// TokenTypeAccess là token dùng để xác thực API
	TokenTypeAccess TokenType = "access"
	// TokenTypeRefresh là token dùng để refresh access token
	TokenTypeRefresh TokenType = "refresh"
	// TokenTypePasswordReset là token dùng để reset password
	TokenTypePasswordReset TokenType = "password_reset"
)

// Token đại diện cho token xác thực
type Token struct {
	ID        string    `db:"id" json:"id" gorm:"primaryKey"`
	UserID    int       `db:"user_id" json:"user_id"`
	TokenType TokenType `db:"token_type" json:"token_type"`
	Value     string    `db:"token_value" json:"token"`
	ExpiresAt time.Time `db:"expires_at" json:"expires_at"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
}

// IsExpired kiểm tra token có hết hạn chưa
func (t *Token) IsExpired() bool {
	return time.Now().After(t.ExpiresAt)
}
