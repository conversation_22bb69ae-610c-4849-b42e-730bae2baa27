package service

import (
	"context"
	"errors"
	"fmt"
	"time"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"golang.org/x/crypto/bcrypt"
)

// Constants for tracing attributes
const (
	AttrUserEmailDomain  = "user.email_domain"
	AttrUserTenantID     = "user.tenant_id"
	AttrUserID           = "user.id"
	AttrUserStatus       = "user.status"
	AttrAuthFailedReason = "auth.failed_reason"
	AttrAuthSuccess      = "auth.success"
)

// Service triển khai AuthService interface
type Service struct {
	repo   internal.Repository
	config internal.AuthConfig
	logger logger.Logger
}

// NewService tạo một auth service mới
func NewService(repo internal.Repository, config internal.AuthConfig, log logger.Logger) internal.AuthService {
	// Đặt giá trị mặc định
	if config.AccessTokenExpiry == 0 {
		config.AccessTokenExpiry = 15 * time.Minute
	}
	if config.RefreshTokenExpiry == 0 {
		config.RefreshTokenExpiry = 7 * 24 * time.Hour // 7 ngày
	}

	return &Service{
		repo:   repo,
		config: config,
		logger: log,
	}
}

// Register đăng ký người dùng mới
func (s *Service) Register(ctx context.Context, req dto.RegisterRequest) (*internal.UserInfo, error) {
	// Tạo span cho toàn bộ quá trình đăng ký
	ctx, span := tracing.StartSpan(ctx, "auth-service", "register")
	defer span.End()

	// Thêm thuộc tính vào span
	tracing.AddSpanAttributes(ctx,
		attribute.String("auth.username", req.Username),
		attribute.String("auth.email", req.Email),
		attribute.String("auth.user_type", req.UserType),
	)

	// Kiểm tra username đã tồn tại chưa
	err := tracing.WithSpan(ctx, "auth-service", "check_existing_username", func(ctx context.Context) error {
		_, err := s.repo.GetUserByUsername(ctx, req.Username)
		if err == nil {
			tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "username_exists"))
			return internal.ErrUserAlreadyExists
		} else if !errors.Is(err, internal.ErrUserNotFound) {
			s.logger.Error("Failed to check existing user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Kiểm tra email đã tồn tại chưa
	if req.Email != "" {
		err := tracing.WithSpan(ctx, "auth-service", "check_existing_email", func(ctx context.Context) error {
			_, err := s.repo.GetUserByEmail(ctx, req.Email)
			if err == nil {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "email_exists"))
				return internal.ErrUserAlreadyExists
			} else if !errors.Is(err, internal.ErrUserNotFound) {
				s.logger.Error("Failed to check existing email", logger.String("error", err.Error()))
				return err
			}
			return nil
		})

		if err != nil {
			tracing.RecordError(ctx, err)
			return nil, err
		}
	}

	// Tạo user mới
	user := &internal.User{
		Username: req.Username,
		Email:    req.Email,
		FullName: req.FullName,
		IsActive: true,
	}

	// Lưu vào database
	err = tracing.WithSpan(ctx, "auth-service", "create_user", func(ctx context.Context) error {
		if err := s.repo.CreateUser(ctx, user, req.Password); err != nil {
			s.logger.Error("Failed to create user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "User registered successfully")
	tracing.AddSpanAttributes(ctx,
		attribute.Int("auth.user_id", user.ID),
		attribute.Bool("auth.success", true),
	)

	// Trả về thông tin user
	return &internal.UserInfo{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
	}, nil
}

// Login xác thực người dùng và trả về token
func (s *Service) Login(ctx context.Context, req dto.LoginRequest) (*dto.LoginResponse, error) {
	// Tạo span cho toàn bộ quá trình đăng nhập
	ctx, span := tracing.StartSpan(ctx, "auth-service", "login")
	defer span.End()

	// Thêm thuộc tính vào span (che dấu thông tin nhạy cảm)
	tracing.AddSpanAttributes(ctx,
		attribute.String("auth.email", req.Email),
		attribute.Bool("auth.admin_login", req.AdminLogin),
	)

	// Lấy user từ database bằng email
	var user *internal.User
	err := tracing.WithSpan(ctx, "auth-service", "get_user_by_email", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByEmail(ctx, req.Email)
		if err != nil {
			if errors.Is(err, internal.ErrUserNotFound) {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "user_not_found"))
				return internal.ErrInvalidCredentials
			}
			s.logger.Error("Failed to get user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Kiểm tra password
	err = tracing.WithSpan(ctx, "auth-service", "verify_password", func(ctx context.Context) error {
		if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
			s.logger.Warn("Invalid password attempt", logger.String("email", req.Email))
			tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "invalid_password"))
			return internal.ErrInvalidCredentials
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Tạo JWT token
	var accessToken string
	err = tracing.WithSpan(ctx, "auth-service", "generate_tokens", func(ctx context.Context) error {
		var err error
		accessToken, _, err = s.generateJWTToken(user.ID, user.Username, s.config.AccessTokenExpiry)
		if err != nil {
			s.logger.Error("Failed to generate access token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Tạo refresh token
	refreshToken := uuid.NewString()
	refreshExpiry := time.Now().Add(s.config.RefreshTokenExpiry)

	// Lưu refresh token vào database
	err = tracing.WithSpan(ctx, "auth-service", "save_refresh_token", func(ctx context.Context) error {
		token := &internal.Token{
			UserID:    user.ID,
			TokenType: internal.TokenTypeRefresh,
			Value:     refreshToken,
			ExpiresAt: refreshExpiry,
		}

		if err := s.repo.CreateToken(ctx, token); err != nil {
			s.logger.Error("Failed to save refresh token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Login successful")
	tracing.AddSpanAttributes(ctx,
		attribute.Int("auth.user_id", user.ID),
		attribute.Bool("auth.success", true),
	)

	// Trả về response
	return &dto.LoginResponse{
		AccessToken:           accessToken,
		AccessTokenExpiresIn:  int(s.config.AccessTokenExpiry.Seconds()),
		RefreshToken:          refreshToken,
		RefreshTokenExpiresIn: int(s.config.RefreshTokenExpiry.Seconds()),
		TokenType:             "Bearer",
		UserID:                int64(user.ID),
		Email:                 user.Email,
		TenantID:              0, // TODO: Add tenant support when available
	}, nil
}

// RefreshToken làm mới access token
func (s *Service) RefreshToken(ctx context.Context, refreshToken string) (*dto.LoginResponse, error) {
	// Tạo span cho toàn bộ quá trình làm mới token
	ctx, span := tracing.StartSpan(ctx, "auth-service", "refresh_token")
	defer span.End()

	// Che giấu token trong span
	if len(refreshToken) > 8 {
		maskedToken := refreshToken[0:8] + "..."
		tracing.AddSpanAttributes(ctx, attribute.String("auth.refresh_token", maskedToken))
	}

	// Lấy token từ database
	var token *internal.Token
	err := tracing.WithSpan(ctx, "auth-service", "get_token", func(ctx context.Context) error {
		var err error
		token, err = s.repo.GetTokenByValue(ctx, refreshToken, internal.TokenTypeRefresh)
		if err != nil {
			if errors.Is(err, internal.ErrTokenNotFound) {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "token_not_found"))
				return internal.ErrInvalidToken
			}
			s.logger.Error("Failed to get refresh token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Kiểm tra token có hết hạn không
	if token.IsExpired() {
		tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "token_expired"))

		// Xóa token đã hết hạn
		_ = tracing.WithSpan(ctx, "auth-service", "delete_expired_token", func(ctx context.Context) error {
			if err := s.repo.DeleteToken(ctx, token.ID); err != nil {
				s.logger.Error("Failed to delete expired token", logger.String("error", err.Error()))
			}
			return nil
		})

		tracing.RecordError(ctx, internal.ErrExpiredToken)
		return nil, internal.ErrExpiredToken
	}

	// Lấy user từ database
	var user *internal.User
	err = tracing.WithSpan(ctx, "auth-service", "get_user", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByID(ctx, token.UserID)
		if err != nil {
			s.logger.Error("Failed to get user for refresh token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Tạo access token mới
	var accessToken string
	err = tracing.WithSpan(ctx, "auth-service", "generate_access_token", func(ctx context.Context) error {
		var err error
		accessToken, _, err = s.generateJWTToken(user.ID, user.Username, s.config.AccessTokenExpiry)
		if err != nil {
			s.logger.Error("Failed to generate new access token", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Đánh dấu thành công trong span
	tracing.SetSpanStatus(ctx, codes.Ok, "Token refreshed successfully")
	tracing.AddSpanAttributes(ctx,
		attribute.Int("auth.user_id", user.ID),
		attribute.Bool("auth.success", true),
	)

	// Trả về response
	return &dto.LoginResponse{
		AccessToken:           accessToken,
		AccessTokenExpiresIn:  int(s.config.AccessTokenExpiry.Seconds()),
		RefreshToken:          refreshToken, // Giữ nguyên refresh token
		RefreshTokenExpiresIn: int(s.config.RefreshTokenExpiry.Seconds()),
		TokenType:             "Bearer",
		UserID:                int64(user.ID),
		Email:                 user.Email,
		TenantID:              0, // TODO: Add tenant support when available
	}, nil
}

// ValidateToken kiểm tra tính hợp lệ của token
func (s *Service) ValidateToken(tokenString string) (map[string]interface{}, error) {
	// Parse token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Kiểm tra signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.JWTSecret), nil
	})

	if err != nil {
		return nil, internal.ErrInvalidToken
	}

	// Kiểm tra tính hợp lệ
	if !token.Valid {
		return nil, internal.ErrInvalidToken
	}

	// Lấy claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, internal.ErrInvalidToken
	}

	// Kiểm tra thời hạn
	if exp, ok := claims["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			return nil, internal.ErrExpiredToken
		}
	}

	return claims, nil
}

// generateJWTToken tạo JWT token
func (s *Service) generateJWTToken(userID int, username string, expiry time.Duration) (string, time.Time, error) {
	expiryTime := time.Now().Add(expiry)

	// Tạo token claims
	claims := jwt.MapClaims{
		"sub":  fmt.Sprintf("%d", userID),
		"user": username,
		"exp":  expiryTime.Unix(),
		"iat":  time.Now().Unix(),
		"jti":  uuid.NewString(),
	}

	// Tạo token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Ký token
	tokenString, err := token.SignedString([]byte(s.config.JWTSecret))
	if err != nil {
		return "", time.Time{}, err
	}

	return tokenString, expiryTime, nil
}
