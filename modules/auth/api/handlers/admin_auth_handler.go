package handlers

import (
	"net/http"

	"wnapi/internal/pkg/response"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
)

type AdminAuthHandler struct {
	authService internal.AuthService
}

func NewAdminAuthHandler(authService internal.AuthService) *AdminAuthHandler {
	return &AdminAuthHandler{authService: authService}
}

func (h *AdminAuthHandler) Login(c *gin.Context) {
	// Bắt đầu span cho operation login
	ctx, span := tracing.StartGinSpan(c, "auth", "login")
	defer span.End()

	var req dto.LoginRequest
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		// Ghi lại lỗi vào span
		tracing.RecordGinError(c, err)
		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", "invalid_request"))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.BadRequest(c, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Thêm thông tin vào span
	tracing.AddGinSpanAttributes(c,
		attribute.String("auth.email", req.Email),
		attribute.Bool("auth.admin_login", req.AdminLogin),
	)

	// Thêm event vào span
	tracing.AddGinSpanEvent(c, "login_attempt",
		attribute.String("auth.method", "credentials"),
	)

	// Thêm tham số để chỉ định rằng đây là đăng nhập admin
	req.AdminLogin = true

	resp, err := h.authService.Login(ctx, req)
	if err != nil {
		// Ghi lại lỗi và phân loại lỗi trong span
		tracing.RecordGinError(c, err)

		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	// Đánh dấu span là thành công
	tracing.SetSpanStatus(ctx, codes.Ok, "Login successful")
	tracing.AddGinSpanAttributes(c,
		attribute.Int64("auth.user_id", resp.UserID),
		attribute.Bool("auth.success", true),
	)
	tracing.AddGinSpanEvent(c, "login_success")

	response.Success(c, resp, nil)
}

// Logout - TODO: Implement when session management is available
func (h *AdminAuthHandler) Logout(c *gin.Context) {
	// Bắt đầu span cho operation logout
	ctx, span := tracing.StartGinSpan(c, "auth", "logout")
	defer span.End()

	tracing.AddGinSpanAttributes(c, attribute.String("auth.operation", "logout"))
	tracing.SetSpanStatus(ctx, codes.Error, "Logout not implemented")

	response.Error(c, http.StatusNotImplemented, "Logout functionality not yet implemented", "NOT_IMPLEMENTED")
}

func (h *AdminAuthHandler) Register(c *gin.Context) {
	// Bắt đầu span cho operation register
	ctx, span := tracing.StartGinSpan(c, "auth", "register")
	defer span.End()

	var req dto.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Ghi lại lỗi vào span
		tracing.RecordGinError(c, err)

		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			details := make([]interface{}, len(validationErrors))
			for i, e := range validationErrors {
				fieldName := toSnakeCase(e.Field())
				details[i] = map[string]string{
					"field":   fieldName,
					"message": getValidationErrorMessage(e),
				}

				// Thêm thông tin lỗi validation vào span
				tracing.AddGinSpanAttributes(c, attribute.String("validation.error."+fieldName, e.Tag()))
			}

			tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", "validation_error"))
			response.ErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "VALIDATION_ERROR", details)
			return
		}

		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", "invalid_request"))
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.BadRequest(c, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Thêm thông tin vào span
	tracing.AddGinSpanAttributes(c,
		attribute.String("auth.username", req.Username),
		attribute.String("auth.email", req.Email),
		attribute.String("auth.user_type", req.UserType),
	)

	// Thêm event vào span
	tracing.AddGinSpanEvent(c, "register_attempt")

	// Always set user_type to 'tenant' for admin registration
	req.UserType = "tenant"

	resp, err := h.authService.Register(ctx, req)
	if err != nil {
		// Ghi lại lỗi vào span
		tracing.RecordGinError(c, err)

		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	// Đánh dấu span là thành công
	tracing.SetSpanStatus(ctx, codes.Ok, "Registration successful")
	tracing.AddGinSpanAttributes(c,
		attribute.Int("auth.user_id", resp.ID),
		attribute.Bool("auth.success", true),
	)
	tracing.AddGinSpanEvent(c, "register_success")

	response.Success(c, resp, nil)
}

func (h *AdminAuthHandler) RefreshToken(c *gin.Context) {
	// Bắt đầu span cho operation refresh token
	ctx, span := tracing.StartGinSpan(c, "auth", "refresh_token")
	defer span.End()

	var req dto.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Ghi lại lỗi vào span
		tracing.RecordGinError(c, err)
		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", "invalid_request"))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.BadRequest(c, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Che giấu token trong span (chỉ lưu một phần)
	if len(req.RefreshToken) > 8 {
		maskedToken := req.RefreshToken[0:8] + "..."
		tracing.AddGinSpanAttributes(c, attribute.String("auth.refresh_token", maskedToken))
	}

	// Thêm event vào span
	tracing.AddGinSpanEvent(c, "token_refresh_attempt")

	resp, err := h.authService.RefreshToken(ctx, req.RefreshToken)
	if err != nil {
		// Ghi lại lỗi vào span
		tracing.RecordGinError(c, err)

		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", errResp.ErrorCode))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	// Đánh dấu span là thành công
	tracing.SetSpanStatus(ctx, codes.Ok, "Token refresh successful")
	tracing.AddGinSpanAttributes(c,
		attribute.Int64("auth.user_id", resp.UserID),
		attribute.Bool("auth.success", true),
	)
	tracing.AddGinSpanEvent(c, "token_refresh_success")

	response.Success(c, resp, nil)
}

// ChangePassword - TODO: Implement when password change functionality is available
func (h *AdminAuthHandler) ChangePassword(c *gin.Context) {
	// Bắt đầu span cho operation change password
	ctx, span := tracing.StartGinSpan(c, "auth", "change_password")
	defer span.End()

	tracing.AddGinSpanAttributes(c, attribute.String("auth.operation", "change_password"))
	tracing.SetSpanStatus(ctx, codes.Error, "Change password not implemented")

	response.Error(c, http.StatusNotImplemented, "Change password functionality not yet implemented", "NOT_IMPLEMENTED")
}

func getValidationErrorMessage(e validator.FieldError) string {
	switch e.Tag() {
	case "required":
		return "Trường này không được để trống"
	case "email":
		return "Định dạng email không hợp lệ"
	case "min":
		return "Giá trị quá ngắn"
	case "max":
		return "Giá trị quá dài"
	case "eqfield":
		return "Các trường không khớp"
	case "containsany":
		switch e.Param() {
		case "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
			return "Phải chứa ít nhất một chữ hoa"
		case "abcdefghijklmnopqrstuvwxyz":
			return "Phải chứa ít nhất một chữ thường"
		case "0123456789":
			return "Phải chứa ít nhất một số"
		case "!@#$%^&*()_+-=[]{}|;':\",./<>?":
			return "Phải chứa ít nhất một ký tự đặc biệt"
		}
		return "Giá trị không hợp lệ"
	default:
		return "Giá trị không hợp lệ"
	}
}

func toSnakeCase(input string) string {
	if input == "Username" {
		return "username"
	} else if input == "Email" {
		return "email"
	} else if input == "Password" {
		return "password"
	} else if input == "FullName" {
		return "full_name"
	}
	return input
}
