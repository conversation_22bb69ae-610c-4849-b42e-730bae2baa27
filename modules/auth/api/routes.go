package api

import (
	"fmt"
	"wnapi/internal/core"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/api/handlers"
	"wnapi/modules/auth/internal"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module Auth
type Handler struct {
	adminAuthHandler *handlers.AdminAuthHandler
	routes           []string
}

// NewHandler tạo một handler mới
func NewHandler(authService internal.AuthService) *Handler {
	return &Handler{
		adminAuthHandler: handlers.NewAdminAuthHandler(authService),
		routes:           make([]string, 0),
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Auth
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/v1/auth")

	// Thêm middleware tracing cho tất cả các route auth
	apiGroup.Use(tracing.GinMiddleware("auth"))

	// L<PERSON>u lại danh sách các route để hiển thị
	basePath := "/api/v1/auth"

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/healthy", basePath))

	// ===== BASIC AUTH ROUTES =====
	// Không yêu cầu xác thực
	apiGroup.POST("/login", h.adminAuthHandler.Login)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/login", basePath))

	apiGroup.POST("/signin", h.adminAuthHandler.Login) // Alias for login
	h.routes = append(h.routes, fmt.Sprintf("POST %s/signin", basePath))

	apiGroup.POST("/register", h.adminAuthHandler.Register)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/register", basePath))

	apiGroup.POST("/signup", h.adminAuthHandler.Register) // Alias for register
	h.routes = append(h.routes, fmt.Sprintf("POST %s/signup", basePath))

	apiGroup.POST("/refresh-token", h.adminAuthHandler.RefreshToken)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/refresh-token", basePath))

	apiGroup.POST("/token/refresh", h.adminAuthHandler.RefreshToken) // Alias for refresh-token
	h.routes = append(h.routes, fmt.Sprintf("POST %s/token/refresh", basePath))

	// TODO: Add password reset routes when handlers are implemented
	// TODO: Add email verification routes when handlers are implemented

	// Yêu cầu xác thực - sẽ được thêm sau khi có JWT middleware
	// authenticated := apiGroup.Group("/")
	// authenticated.Use(jwtAuthMiddleware())
	// {
	//     authenticated.POST("/logout", h.adminAuthHandler.Logout)
	//     authenticated.PUT("/change-password", h.adminAuthHandler.ChangePassword)
	// }

	return nil
}

// ListRoutes trả về danh sách tất cả các routes đã đăng ký
func (h *Handler) ListRoutes() []string {
	return h.routes
}

// PrintRoutes in ra console danh sách tất cả các routes đã đăng ký
func (h *Handler) PrintRoutes() {
	fmt.Println("=== AUTH MODULE ROUTES ===")
	for _, route := range h.routes {
		fmt.Println(route)
	}
	fmt.Println("=========================")
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "auth",
		"message": "Auth module is running",
	})
}

// AuthHandler is an alias for Handler to maintain backward compatibility
type AuthHandler = Handler

// NewAuthHandler creates a new AuthHandler (alias for NewHandler)
func NewAuthHandler(authService internal.AuthService) *AuthHandler {
	return NewHandler(authService)
}
