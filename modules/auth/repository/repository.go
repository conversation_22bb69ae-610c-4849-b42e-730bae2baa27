package repository

import (
	"context"

	"wnapi/modules/auth/models"
)

type AuthRepository interface {
	CreateSession(ctx context.Context, session *models.Session) error
	GetSession(ctx context.Context, sessionID string) (*models.Session, error)
	DeleteSession(ctx context.Context, sessionID string) error
}

// SessionRepository interface for session management
// TODO: Implement when session functionality is needed
type SessionRepository interface {
	CreateSession(ctx context.Context, session *models.Session) error
	GetSession(ctx context.Context, sessionID string) (*models.Session, error)
	DeleteSession(ctx context.Context, sessionID string) error
	DeleteExpiredSessions(ctx context.Context) error
}

// EmailVerificationRepository interface for email verification management
// TODO: Implement when email verification functionality is needed
type EmailVerificationRepository interface {
	Create(ctx context.Context, verification *models.EmailVerification) error
	GetByToken(ctx context.Context, token string) (*models.EmailVerification, error)
	GetByUserID(ctx context.Context, userID int64) (*models.EmailVerification, error)
	Update(ctx context.Context, verification *models.EmailVerification) error
	Delete(ctx context.Context, id int64) error
}
