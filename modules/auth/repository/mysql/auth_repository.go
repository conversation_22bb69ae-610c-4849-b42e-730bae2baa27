package mysql

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/internal"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	"go.opentelemetry.io/otel/attribute"
	"golang.org/x/crypto/bcrypt"
)

// mysqlRepository triển khai Repository interface sử dụng sqlx
type mysqlRepository struct {
	db     *sqlx.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	db := dbManager.GetDB()
	if db == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	return &mysqlRepository{
		db:     db,
		logger: logger,
	}, nil
}

// CreateUser tạo người dùng mới
func (r *mysqlRepository) CreateUser(ctx context.Context, user *internal.User, password string) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "create_user"),
		attribute.String("auth.username", user.Username),
		attribute.String("auth.email", user.Email),
	)

	// Hash mật khẩu
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		tracing.RecordError(ctx, err)
		return err
	}

	user.PasswordHash = string(hashedPassword)
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	query := `INSERT INTO users (tenant_id, username, email, password_hash, full_name, user_type, status, is_email_verified, created_at, updated_at)
              VALUES (:tenant_id, :username, :email, :password_hash, :full_name, :user_type, :status, :is_email_verified, :created_at, :updated_at)`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	// Thực thi truy vấn
	result, err := r.db.NamedExecContext(ctx, query, user)
	if err != nil {
		r.logger.Error("Không thể tạo người dùng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	// Lấy ID được tạo
	id, err := result.LastInsertId()
	if err != nil {
		r.logger.Error("Không thể lấy ID người dùng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	user.ID = int(id)
	tracing.AddSpanAttributes(ctx, attribute.Int("auth.user_id", user.ID))

	return nil
}

// GetUserByID lấy thông tin người dùng theo ID
func (r *mysqlRepository) GetUserByID(ctx context.Context, id int) (*internal.User, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "get_user_by_id"),
		attribute.Int("auth.user_id", id),
	)

	var user internal.User
	query := `SELECT user_id, tenant_id, username, email, password_hash, full_name, user_type, status, is_email_verified, created_at, updated_at, last_login
              FROM users WHERE user_id = ?`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	err := r.db.GetContext(ctx, &user, query, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin người dùng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.String("auth.username", user.Username),
		attribute.String("auth.email", user.Email),
	)

	return &user, nil
}

// GetUserByUsername lấy thông tin người dùng theo tên đăng nhập
func (r *mysqlRepository) GetUserByUsername(ctx context.Context, username string) (*internal.User, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "get_user_by_username"),
		attribute.String("auth.username", username),
	)

	var user internal.User
	query := `SELECT user_id, tenant_id, username, email, password_hash, full_name, user_type, status, is_email_verified, created_at, updated_at, last_login
              FROM users WHERE username = ?`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	err := r.db.GetContext(ctx, &user, query, username)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin người dùng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", user.ID),
		attribute.String("auth.email", user.Email),
	)

	return &user, nil
}

// GetUserByEmail lấy thông tin người dùng theo email
func (r *mysqlRepository) GetUserByEmail(ctx context.Context, email string) (*internal.User, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "get_user_by_email"),
		attribute.String("auth.email", email),
	)

	var user internal.User
	query := `SELECT user_id, tenant_id, username, email, password_hash, full_name, user_type, status, is_email_verified, created_at, updated_at, last_login
              FROM users WHERE email = ?`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	err := r.db.GetContext(ctx, &user, query, email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin người dùng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", user.ID),
		attribute.String("auth.username", user.Username),
	)

	return &user, nil
}

// UpdateUser cập nhật thông tin người dùng
func (r *mysqlRepository) UpdateUser(ctx context.Context, user *internal.User) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "update_user"),
		attribute.Int("auth.user_id", user.ID),
		attribute.String("auth.username", user.Username),
		attribute.String("auth.email", user.Email),
	)

	user.UpdatedAt = time.Now()

	query := `UPDATE users SET
              username = :username,
              email = :email,
              password_hash = :password_hash,
              full_name = :full_name,
              user_type = :user_type,
              status = :status,
              is_email_verified = :is_email_verified,
              updated_at = :updated_at
              WHERE user_id = :user_id`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	_, err := r.db.NamedExecContext(ctx, query, user)
	if err != nil {
		r.logger.Error("Không thể cập nhật người dùng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	return nil
}

// DeleteUser xóa người dùng
func (r *mysqlRepository) DeleteUser(ctx context.Context, id int) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "users"),
		attribute.String("db.operation", "delete_user"),
		attribute.Int("auth.user_id", id),
	)

	query := "DELETE FROM users WHERE user_id = ?"
	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	_, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.Error("Không thể xóa người dùng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	return nil
}

// CreateToken tạo token mới (sử dụng auth_sessions table)
func (r *mysqlRepository) CreateToken(ctx context.Context, token *internal.Token) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "create_token"),
		attribute.Int("auth.user_id", token.UserID),
		attribute.String("auth.token_type", string(token.TokenType)),
	)

	// Nếu ID token chưa được thiết lập, tạo ID mới
	if token.ID == "" {
		token.ID = uuid.New().String()
	}

	token.CreatedAt = time.Now()

	query := `INSERT INTO auth_sessions (id, user_id, access_token, refresh_token, expires_at, created_at, updated_at)
              VALUES (:id, :user_id, :access_token, :refresh_token, :expires_at, :created_at, :updated_at)`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	_, err := r.db.NamedExecContext(ctx, query, token)
	if err != nil {
		r.logger.Error("Không thể tạo token", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	// Che giấu thông tin nhạy cảm trong span
	if len(token.Value) > 8 {
		maskedToken := token.Value[0:8] + "..."
		tracing.AddSpanAttributes(ctx, attribute.String("auth.token_id", token.ID))
		tracing.AddSpanAttributes(ctx, attribute.String("auth.token_value_masked", maskedToken))
	}

	return nil
}

// GetTokenByValue lấy token theo giá trị và loại (sử dụng auth_sessions table)
func (r *mysqlRepository) GetTokenByValue(ctx context.Context, tokenValue string, tokenType internal.TokenType) (*internal.Token, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "get_token_by_value"),
		attribute.String("auth.token_type", string(tokenType)),
	)

	// Che giấu thông tin nhạy cảm trong span
	if len(tokenValue) > 8 {
		maskedToken := tokenValue[0:8] + "..."
		tracing.AddSpanAttributes(ctx, attribute.String("auth.token_value_masked", maskedToken))
	}

	var token internal.Token
	query := `SELECT id, user_id, access_token, refresh_token, expires_at, created_at, updated_at
              FROM auth_sessions WHERE access_token = ? OR refresh_token = ?`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	err := r.db.GetContext(ctx, &token, query, tokenValue, tokenValue)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrTokenNotFound
		}
		r.logger.Error("Không thể lấy token", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", token.UserID),
		attribute.String("auth.token_id", token.ID),
		attribute.Bool("auth.token_expired", token.IsExpired()),
	)

	return &token, nil
}

// DeleteToken xóa token (sử dụng auth_sessions table)
func (r *mysqlRepository) DeleteToken(ctx context.Context, id string) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "delete_token"),
		attribute.String("auth.token_id", id),
	)

	query := "DELETE FROM auth_sessions WHERE id = ?"
	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	_, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.Error("Không thể xóa token", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	return nil
}

// DeleteExpiredTokens xóa tất cả token đã hết hạn (sử dụng auth_sessions table)
func (r *mysqlRepository) DeleteExpiredTokens(ctx context.Context) error {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "auth_sessions")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_sessions"),
		attribute.String("db.operation", "delete_expired_tokens"),
	)

	query := "DELETE FROM auth_sessions WHERE expires_at < ?"
	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	result, err := r.db.ExecContext(ctx, query, time.Now())
	if err != nil {
		r.logger.Error("Không thể xóa token hết hạn", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	// Thêm thông tin về số lượng token đã xóa
	rowsAffected, _ := result.RowsAffected()
	tracing.AddSpanAttributes(ctx, attribute.Int64("db.rows_affected", rowsAffected))

	return nil
}
