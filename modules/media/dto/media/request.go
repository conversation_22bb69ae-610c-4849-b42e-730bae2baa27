package media

import (
	"mime/multipart"

	"github.com/webnew/wn-backend-v2/modules/media/dto/common"
)

// UploadMediaRequest định nghĩa request body cho API upload media
type UploadMediaRequest struct {
	Description string                `form:"description" json:"description"`
	IsPublic    bool                  `form:"is_public" json:"is_public"`
	Tags        []string              `form:"tags" json:"tags"`
	File        *multipart.FileHeader `form:"file" json:"-"`
}

// UpdateMediaRequest định nghĩa request body cho API update media
type UpdateMediaRequest struct {
	Description *string   `json:"description,omitempty"`
	IsPublic    *bool     `json:"is_public,omitempty"`
	Tags        *[]string `json:"tags,omitempty"`
}

// ListMediaRequest định nghĩa request parameters cho API list media
type ListMediaRequest struct {
	common.CursorPaginationRequest
	MediaType string   `form:"media_type" json:"media_type"`
	Status    string   `form:"status" json:"status"`
	IsPublic  *bool    `form:"is_public" json:"is_public"`
	Tags      []string `form:"tags" json:"tags"`
	Search    string   `form:"search" json:"search"`
}

// DeleteMediaRequest định nghĩa request parameters cho API delete media
type DeleteMediaRequest struct {
	Permanent bool `form:"permanent" json:"permanent"`
}
