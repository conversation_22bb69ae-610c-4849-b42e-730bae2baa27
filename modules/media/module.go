package media

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/media/api/routes"
	"github.com/webnew/wn-backend-v2/modules/media/configs"
	"github.com/webnew/wn-backend-v2/modules/media/repository"
	"github.com/webnew/wn-backend-v2/modules/media/repository/mysql"
	"github.com/webnew/wn-backend-v2/modules/media/repository/s3"
	"github.com/webnew/wn-backend-v2/modules/media/service/common"
	"github.com/webnew/wn-backend-v2/modules/media/service/user"
	"github.com/webnew/wn-backend-v2/modules/media/tracing"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	"github.com/webnew/wn-backend-v2/pkg/middleware"
)

var (
	db           *sqlx.DB
	config       *configs.Config
	mediaService common.MediaService
	mediaRepo    *mysql.MediaRepository
	storageRepo  repository.StorageRepository
	folderRepo   *mysql.MediaFolderRepository
	mediaRouter  *gin.RouterGroup
	adminRouter  *gin.RouterGroup
	jwtService   *auth.JWTService
	permService  middleware.PermissionService
)

// DummyPermissionService là triển khai giả cho PermissionService
type DummyPermissionService struct{}

// CheckPermission luôn trả về true cho mọi truy vấn quyền
func (s *DummyPermissionService) CheckPermission(ctx *gin.Context, tenantID, userID uint, permission string) (bool, error) {
	// Trong môi trường phát triển, luôn cho phép mọi quyền truy cập
	return true, nil
}

// NewDummyPermissionService tạo một instance mới của DummyPermissionService
func NewDummyPermissionService() middleware.PermissionService {
	return &DummyPermissionService{}
}

// Init khởi tạo module
func Init() error {
	var err error

	// Load config
	config, err = configs.LoadConfig()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Initialize tracing if enabled
	if config.Tracing != nil && config.Tracing.Enabled {
		log.Printf("Initializing tracing for media-service with exporter type: %s", config.Tracing.ExporterType)
		// Initialize tracing
		if err := tracing.InitTracing(config.Tracing); err != nil {
			log.Printf("Warning: failed to initialize tracing: %v", err)
		}
	}

	// Connect to database
	db, err = initDatabase(config.DB)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// Init repositories
	mediaRepo = mysql.NewMediaRepository(db)
	storageRepo = s3.NewStorageFactory(config.Storage).NewStorageRepository()
	folderRepo = mysql.NewMediaFolderRepository(db)

	// Init services
	baseMediaService := user.NewMediaService(mediaRepo, storageRepo)

	// Wrap with tracing if enabled
	if config.Tracing != nil && config.Tracing.Enabled {
		mediaService = user.NewTracedMediaService(baseMediaService, config.Tracing)
	} else {
		mediaService = baseMediaService
	}

	// Init JWT service
	jwtService = initJWTService(config.JWT)

	// Init permission service với triển khai giả
	permService = NewDummyPermissionService()

	return nil
}

// RegisterHTTPRoutes đăng ký HTTP routes
func RegisterHTTPRoutes(router *gin.Engine) {
	if router == nil {
		log.Println("Warning: router is nil. Skipping HTTP routes registration.")
		return
	}

	// Create path groups
	mediaRouter = router.Group("/api/v1")
	adminRouter = router.Group("/api/v1/admin")

	mediaRouter.GET("media/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": gin.H{
				"code":      http.StatusOK,
				"message":   "Service is healthy",
				"success":   true,
				"path":      c.Request.URL.Path,
				"timestamp": time.Now().Format(time.RFC3339),
			},
		})
	})

	// Register routes
	routes.RegisterUserRoutes(mediaRouter, mediaService, mediaRepo, folderRepo, storageRepo, jwtService, permService)

	// Log all registered routes
	printRegisteredRoutes(router)
}

// RegisterMiddlewares đăng ký middleware
func RegisterMiddlewares(router *gin.Engine) {
	if router == nil {
		log.Println("Warning: router is nil. Skipping middleware registration.")
		return
	}

	// Register tracing middleware if enabled
	if config != nil && config.Tracing != nil && config.Tracing.Enabled {
		log.Println("Registering tracing middleware")
		// Use the tracing middleware
		tracingMiddleware := tracing.NewTracingMiddleware(config.Tracing)
		router.Use(tracingMiddleware.Middleware())
	}
}

// initDatabase khởi tạo kết nối database
func initDatabase(config configs.DBConfig) (*sqlx.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true&charset=utf8mb4&collation=utf8mb4_unicode_ci",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
	)

	db, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		return nil, err
	}

	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)
	db.SetConnMaxLifetime(5 * 60 * 1000) // 5 minutes

	return db, nil
}

// initJWTService khởi tạo JWT service
func initJWTService(config configs.JWTConfig) *auth.JWTService {
	jwtConfig := auth.JWTConfig{
		AccessSigningKey:       config.AccessSecret,
		RefreshSigningKey:      config.RefreshSecret,
		AccessTokenExpiration:  config.AccessTokenDuration,
		RefreshTokenExpiration: config.RefreshTokenDuration,
		Issuer:                 config.Issuer,
	}
	return auth.NewJWTService(jwtConfig)
}

// Ping kiểm tra kết nối database
func Ping(ctx context.Context) error {
	if db == nil {
		return fmt.Errorf("database connection not initialized")
	}
	return db.PingContext(ctx)
}

// printRegisteredRoutes in ra tất cả routes đã đăng ký (chỉ trong môi trường development)
func printRegisteredRoutes(router *gin.Engine) {
	if config == nil || router == nil {
		return
	}

	routes := router.Routes()
	log.Println("=== MEDIA MODULE ROUTES ===")
	for _, route := range routes {
		if route.Method == http.MethodConnect {
			continue
		}
		log.Printf("%s %s", route.Method, route.Path)
	}
	log.Println("===========================")
}
