package user

import (
	"context"
	"mime/multipart"

	"github.com/webnew/wn-backend-v2/modules/media/configs"
	"github.com/webnew/wn-backend-v2/modules/media/dto/media"
	"github.com/webnew/wn-backend-v2/modules/media/models"
	"github.com/webnew/wn-backend-v2/modules/media/service/common"
	"github.com/webnew/wn-backend-v2/modules/media/tracing"
)

// TracedMediaService is a wrapper around MediaService that adds tracing
type TracedMediaService struct {
	service *MediaService
	config  *configs.TracingConfig
}

// Ensure TracedMediaService implements common.MediaService
var _ common.MediaService = (*TracedMediaService)(nil)

// NewTracedMediaService creates a new traced media service
func NewTracedMediaService(service *MediaService, config *configs.TracingConfig) *TracedMediaService {
	return &TracedMediaService{
		service: service,
		config:  config,
	}
}

// Upload traces the upload operation
func (s *TracedMediaService) Upload(ctx context.Context, tenantID string, userID string, req *media.UploadMediaRequest) (*media.UploadMediaResponse, error) {
	// Create a new span for the upload operation
	ctx, span := tracing.TraceUpload(ctx, s.config, "", req.File.Header.Get("Content-Type"), req.File.Size)
	defer tracing.EndSpan(span)

	// Add additional attributes
	if span != nil {
		tracing.AddAttribute(span, "tenant_id", tenantID)
		tracing.AddAttribute(span, "user_id", userID)
		tracing.AddAttribute(span, "filename", req.File.Filename)
	}

	// Call the original service method
	result, err := s.service.Upload(ctx, tenantID, userID, req)

	// Record error if any
	if err != nil && span != nil {
		tracing.RecordError(span, err)
	} else if result != nil && span != nil {
		// Add result attributes
		tracing.AddAttribute(span, "media.file_id", result.ID)
	}

	return result, err
}

// Get traces the get operation
func (s *TracedMediaService) Get(ctx context.Context, tenantID string, id string) (*media.MediaResponse, error) {
	return tracing.TraceServiceWithResult(ctx, s.config, "MediaService", "Get", func(ctx context.Context) (*media.MediaResponse, error) {
		// Add span attributes
		if span := tracing.GetSpanFromContext(ctx); span != nil {
			tracing.AddAttribute(span, "media.file_id", id)
			tracing.AddAttribute(span, "tenant_id", tenantID)
		}

		// Call the original service method
		return s.service.Get(ctx, tenantID, id)
	})
}

// List traces the list operation
func (s *TracedMediaService) List(ctx context.Context, tenantID string, req *media.ListMediaRequest) ([]*media.MediaResponse, string, error) {
	ctx, span := tracing.StartSpan(ctx, s.config, "MediaService.List")
	defer tracing.EndSpan(span)

	if span != nil {
		tracing.AddAttribute(span, "tenant_id", tenantID)
		if req.MediaType != "" {
			tracing.AddAttribute(span, "media.type", req.MediaType)
		}
		if req.Search != "" {
			tracing.AddAttribute(span, "search", req.Search)
		}
	}

	result, nextCursor, err := s.service.List(ctx, tenantID, req)
	if err != nil && span != nil {
		tracing.RecordError(span, err)
	} else if span != nil {
		tracing.AddAttribute(span, "result_count", len(result))
		tracing.AddAttribute(span, "has_more", nextCursor != "")
	}

	return result, nextCursor, err
}

// Update traces the update operation
func (s *TracedMediaService) Update(ctx context.Context, tenantID string, id string, req *media.UpdateMediaRequest) (*media.MediaResponse, error) {
	return tracing.TraceServiceWithResult(ctx, s.config, "MediaService", "Update", func(ctx context.Context) (*media.MediaResponse, error) {
		// Add span attributes
		if span := tracing.GetSpanFromContext(ctx); span != nil {
			tracing.AddAttribute(span, "media.file_id", id)
			tracing.AddAttribute(span, "tenant_id", tenantID)
		}

		// Call the original service method
		return s.service.Update(ctx, tenantID, id, req)
	})
}

// Delete traces the delete operation
func (s *TracedMediaService) Delete(ctx context.Context, tenantID string, id string, permanent bool) error {
	return tracing.TraceService(ctx, s.config, "MediaService", "Delete", func(ctx context.Context) error {
		// Add span attributes
		if span := tracing.GetSpanFromContext(ctx); span != nil {
			tracing.AddAttribute(span, "media.file_id", id)
			tracing.AddAttribute(span, "tenant_id", tenantID)
			tracing.AddAttribute(span, "permanent", permanent)
		}

		// Call the original service method
		return s.service.Delete(ctx, tenantID, id, permanent)
	})
}

// GetFile traces the get file operation
func (s *TracedMediaService) GetFile(ctx context.Context, tenantID string, id string) ([]byte, string, error) {
	ctx, span := tracing.StartSpan(ctx, s.config, "MediaService.GetFile")
	defer tracing.EndSpan(span)

	if span != nil {
		tracing.AddAttribute(span, "media.file_id", id)
		tracing.AddAttribute(span, "tenant_id", tenantID)
	}

	data, contentType, err := s.service.GetFile(ctx, tenantID, id)
	if err != nil && span != nil {
		tracing.RecordError(span, err)
	} else if span != nil {
		tracing.AddAttribute(span, "content_type", contentType)
		tracing.AddAttribute(span, "data_size", len(data))
	}

	return data, contentType, err
}

// ProcessFile traces the process file operation
func (s *TracedMediaService) ProcessFile(ctx context.Context, mediaEntity *models.Media) error {
	return tracing.TraceService(ctx, s.config, "MediaService", "ProcessFile", func(ctx context.Context) error {
		// Add span attributes
		if span := tracing.GetSpanFromContext(ctx); span != nil {
			tracing.AddAttribute(span, "media.file_id", mediaEntity.ID)
			tracing.AddAttribute(span, "tenant_id", mediaEntity.TenantID)
			tracing.AddAttribute(span, "media.type", string(mediaEntity.MediaType))
		}

		// Call the original service method
		return s.service.ProcessFile(ctx, mediaEntity)
	})
}

// SaveFile traces the save file operation
func (s *TracedMediaService) SaveFile(ctx context.Context, tenantID string, file *multipart.FileHeader, mediaType models.MediaType) (*models.Media, error) {
	// Kiểm tra xem tracing có được bật không
	if s.config == nil || !s.config.Enabled {
		// Nếu không bật, gọi trực tiếp service
		return s.service.SaveFile(ctx, tenantID, file, mediaType)
	}

	// Nếu tracing được bật, tiếp tục với tracing
	ctx, span := tracing.StartSpan(ctx, s.config, "MediaService.SaveFile")
	defer tracing.EndSpan(span)

	// Add span attributes
	if span != nil {
		tracing.AddAttribute(span, "tenant_id", tenantID)
		tracing.AddAttribute(span, "media.type", string(mediaType))
		tracing.AddAttribute(span, "filename", file.Filename)
		tracing.AddAttribute(span, "file_size", file.Size)
	}

	// Call the original service method
	result, err := s.service.SaveFile(ctx, tenantID, file, mediaType)
	if err != nil && span != nil {
		tracing.RecordError(span, err)
	}

	return result, err
}

// GetSpanFromContext is a helper function to get the current span from context
func (s *TracedMediaService) GetSpanFromContext(ctx context.Context) interface{} {
	return tracing.GetSpanFromContext(ctx)
}
