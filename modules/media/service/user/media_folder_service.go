package user

import (
	"context"
	"fmt"

	"github.com/gosimple/slug"
	"github.com/webnew/wn-backend-v2/modules/media/dto/folder"
	"github.com/webnew/wn-backend-v2/modules/media/models"
	"github.com/webnew/wn-backend-v2/modules/media/repository"
)

// MediaFolderService triển khai MediaFolderService cho user
type MediaFolderService struct {
	folderRepo repository.MediaFolderRepository
}

// NewMediaFolderService tạo một instance mới của MediaFolderService
func NewMediaFolderService(
	folderRepo repository.MediaFolderRepository,
) *MediaFolderService {
	return &MediaFolderService{
		folderRepo: folderRepo,
	}
}

// Create tạo một thư mục mới
func (s *MediaFolderService) Create(ctx context.Context, tenantID int, userID int, req *folder.CreateFolderRequest) (*folder.FolderResponse, error) {
	// Kiểm tra nếu folder với slug đã tồn tại
	folderSlug := slug.Make(req.Name)
	existingFolder, err := s.folderRepo.GetBySlug(ctx, folderSlug, tenantID)
	if err != nil {
		return nil, err
	}

	if existingFolder != nil {
		return nil, fmt.Errorf("thư mục với tên này đã tồn tại")
	}

	// Kiểm tra parent folder nếu có
	if req.ParentID != nil {
		parentFolder, err := s.folderRepo.GetByID(ctx, *req.ParentID, tenantID)
		if err != nil {
			return nil, err
		}

		if parentFolder == nil {
			return nil, fmt.Errorf("thư mục cha không tồn tại")
		}
	}

	// Tạo entity
	folderEntity := &models.MediaFolder{
		TenantID:    tenantID,
		ParentID:    req.ParentID,
		Name:        req.Name,
		Slug:        folderSlug,
		Description: req.Description,
		IsPublic:    req.IsPublic,
		CreatedBy:   &userID,
		UpdatedBy:   &userID,
	}

	// Lưu vào database
	err = s.folderRepo.Create(ctx, folderEntity)
	if err != nil {
		return nil, err
	}

	// Trả về response
	return s.entityToResponse(folderEntity), nil
}

// Get lấy thông tin chi tiết về một thư mục
func (s *MediaFolderService) Get(ctx context.Context, tenantID int, folderID int) (*folder.FolderResponse, error) {
	// Lấy thông tin từ repository
	folderEntity, err := s.folderRepo.GetByID(ctx, folderID, tenantID)
	if err != nil {
		return nil, err
	}

	if folderEntity == nil {
		return nil, fmt.Errorf("thư mục không tồn tại")
	}

	// Trả về response
	return s.entityToResponse(folderEntity), nil
}

// List lấy danh sách thư mục con
func (s *MediaFolderService) List(ctx context.Context, tenantID int, req *folder.ListFolderRequest) ([]*folder.FolderResponse, string, error) {
	// Tạo parameters cho repository
	params := repository.ListMediaFolderParams{
		TenantID: tenantID,
		ParentID: req.ParentID,
		IsPublic: req.IsPublic,
		Search:   req.Search,
		Cursor:   req.Cursor,
		Limit:    req.Limit,
	}

	// Lấy danh sách từ repository
	folderList, nextCursor, err := s.folderRepo.List(ctx, params)
	if err != nil {
		return nil, "", err
	}

	// Tạo response list
	folderResponseList := make([]*folder.FolderResponse, len(folderList))
	for i, folderEntity := range folderList {
		folderResponseList[i] = s.entityToResponse(folderEntity)
	}

	return folderResponseList, nextCursor, nil
}

// Update cập nhật thông tin thư mục
func (s *MediaFolderService) Update(ctx context.Context, tenantID int, userID int, folderID int, req *folder.UpdateFolderRequest) (*folder.FolderResponse, error) {
	// Lấy thông tin hiện tại
	folderEntity, err := s.folderRepo.GetByID(ctx, folderID, tenantID)
	if err != nil {
		return nil, err
	}

	if folderEntity == nil {
		return nil, fmt.Errorf("thư mục không tồn tại")
	}

	// Kiểm tra parent folder nếu có thay đổi
	if req.ParentID != nil && (folderEntity.ParentID == nil || *folderEntity.ParentID != *req.ParentID) {
		// Kiểm tra parent folder mới
		if *req.ParentID != 0 {
			parentFolder, err := s.folderRepo.GetByID(ctx, *req.ParentID, tenantID)
			if err != nil {
				return nil, err
			}

			if parentFolder == nil {
				return nil, fmt.Errorf("thư mục cha không tồn tại")
			}

			// Đảm bảo không tạo vòng lặp (parent không thể là chính nó hoặc con của nó)
			if *req.ParentID == folderEntity.FolderID {
				return nil, fmt.Errorf("không thể đặt thư mục làm cha của chính nó")
			}
		} else {
			// Nếu parentID = 0, đặt thành NULL (root folder)
			var nilParentID *int = nil
			req.ParentID = nilParentID
		}

		folderEntity.ParentID = req.ParentID
	}

	// Cập nhật các trường khác
	if req.Name != "" && req.Name != folderEntity.Name {
		folderEntity.Name = req.Name
		folderEntity.Slug = slug.Make(req.Name)

		// Kiểm tra nếu slug mới đã tồn tại
		existingFolder, err := s.folderRepo.GetBySlug(ctx, folderEntity.Slug, tenantID)
		if err != nil {
			return nil, err
		}

		if existingFolder != nil && existingFolder.FolderID != folderEntity.FolderID {
			return nil, fmt.Errorf("thư mục với tên này đã tồn tại")
		}
	}

	if req.Description != nil {
		folderEntity.Description = *req.Description
	}

	if req.IsPublic != nil {
		folderEntity.IsPublic = *req.IsPublic
	}

	// Cập nhật người sửa
	folderEntity.UpdatedBy = &userID

	// Lưu thay đổi
	err = s.folderRepo.Update(ctx, folderEntity)
	if err != nil {
		return nil, err
	}

	// Trả về response
	return s.entityToResponse(folderEntity), nil
}

// Delete xóa thư mục
func (s *MediaFolderService) Delete(ctx context.Context, tenantID int, folderID int) error {
	// Kiểm tra thư mục tồn tại
	folderEntity, err := s.folderRepo.GetByID(ctx, folderID, tenantID)
	if err != nil {
		return err
	}

	if folderEntity == nil {
		return fmt.Errorf("thư mục không tồn tại")
	}

	// TODO: Kiểm tra thư mục có chứa media hoặc thư mục con không

	// Xóa thư mục
	return s.folderRepo.Delete(ctx, folderID, tenantID)
}

// entityToResponse chuyển đổi entity sang response
func (s *MediaFolderService) entityToResponse(entity *models.MediaFolder) *folder.FolderResponse {
	return &folder.FolderResponse{
		FolderID:    entity.FolderID,
		ParentID:    entity.ParentID,
		Name:        entity.Name,
		Slug:        entity.Slug,
		Description: entity.Description,
		IsPublic:    entity.IsPublic,
		CreatedAt:   entity.CreatedAt,
		UpdatedAt:   entity.UpdatedAt,
	}
}
