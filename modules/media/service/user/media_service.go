package user

import (
	"context"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/webnew/wn-backend-v2/modules/media/dto/media"
	"github.com/webnew/wn-backend-v2/modules/media/models"
	"github.com/webnew/wn-backend-v2/modules/media/repository"
)

// MediaService triển khai MediaService cho user
type MediaService struct {
	mediaRepo   repository.MediaRepository
	storageRepo repository.StorageRepository
}

// NewMediaService tạo một instance mới của MediaService
func NewMediaService(
	mediaRepo repository.MediaRepository,
	storageRepo repository.StorageRepository,
) *MediaService {
	return &MediaService{
		mediaRepo:   mediaRepo,
		storageRepo: storageRepo,
	}
}

// Upload tải lên một media mới
func (s *MediaService) Upload(ctx context.Context, tenantID string, userID string, req *media.UploadMediaRequest) (*media.UploadMediaResponse, error) {
	// Xác đ<PERSON>nh media type từ content type
	mediaType := getMediaTypeFromContentType(req.File.Header.Get("Content-Type"))

	fmt.Println("mediaType", mediaType)

	// Lưu file vào storage
	mediaEntity, err := s.SaveFile(ctx, tenantID, req.File, mediaType)
	if err != nil {
		return nil, err
	}

	// Cập nhật thông tin bổ sung
	mediaEntity.Description = req.Description
	mediaEntity.IsPublic = req.IsPublic
	mediaEntity.UploadedBy = userID

	// Lưu thông tin vào database
	err = s.mediaRepo.Create(ctx, mediaEntity)
	if err != nil {
		// Nếu thất bại, xóa file từ storage
		_ = s.storageRepo.Delete(ctx, mediaEntity.ObjectKey)
		return nil, err
	}

	// Thêm tags nếu có
	if len(req.Tags) > 0 {
		err = s.mediaRepo.AddTags(ctx, mediaEntity.ID, req.Tags)
		if err != nil {
			return nil, err
		}
	}

	// Xử lý file nếu cần (chạy trong goroutine)
	go func() {
		ctxBg := context.Background()
		_ = s.ProcessFile(ctxBg, mediaEntity)
	}()

	// Lấy URL truy cập
	url, _ := s.storageRepo.GetURL(ctx, mediaEntity.ObjectKey)

	// Trả về response
	return &media.UploadMediaResponse{
		ID:     mediaEntity.ID,
		URL:    url,
		Status: string(mediaEntity.Status),
	}, nil
}

// Get lấy thông tin chi tiết về một media
func (s *MediaService) Get(ctx context.Context, tenantID string, id string) (*media.MediaResponse, error) {
	// Lấy thông tin từ repository
	mediaEntity, err := s.mediaRepo.GetByID(ctx, id, tenantID)
	if err != nil {
		return nil, err
	}

	if mediaEntity == nil {
		return nil, fmt.Errorf("media không tồn tại")
	}

	// Lấy tags
	tags, err := s.mediaRepo.GetTags(ctx, id)
	if err != nil {
		return nil, err
	}

	// Lấy URL truy cập
	url, _ := s.storageRepo.GetURL(ctx, mediaEntity.ObjectKey)

	// Tạo response
	return &media.MediaResponse{
		ID:               mediaEntity.ID,
		MediaType:        string(mediaEntity.MediaType),
		Filename:         mediaEntity.Filename,
		OriginalFilename: mediaEntity.OriginalFilename,
		ContentType:      mediaEntity.ContentType,
		Size:             mediaEntity.Size,
		Status:           string(mediaEntity.Status),
		PublicURL:        url,
		Description:      mediaEntity.Description,
		IsPublic:         mediaEntity.IsPublic,
		Tags:             tags,
		Metadata:         mediaEntity.Metadata,
		Properties:       mediaEntity.Properties,
		CreatedAt:        mediaEntity.CreatedAt,
		UpdatedAt:        mediaEntity.UpdatedAt,
	}, nil
}

// List lấy danh sách media
func (s *MediaService) List(ctx context.Context, tenantID string, req *media.ListMediaRequest) ([]*media.MediaResponse, string, error) {
	// Tạo parameters cho repository
	params := repository.ListMediaParams{
		TenantID:  tenantID,
		MediaType: req.MediaType,
		Status:    req.Status,
		IsPublic:  req.IsPublic,
		Tags:      req.Tags,
		Search:    req.Search,
		Cursor:    req.Cursor,
		Limit:     req.Limit,
	}

	// Lấy danh sách từ repository
	mediaList, nextCursor, err := s.mediaRepo.List(ctx, params)
	if err != nil {
		return nil, "", err
	}

	// Tạo response list
	mediaResponseList := make([]*media.MediaResponse, len(mediaList))
	for i, mediaEntity := range mediaList {
		// Lấy tags
		tags, _ := s.mediaRepo.GetTags(ctx, mediaEntity.ID)

		// Lấy URL truy cập
		url, _ := s.storageRepo.GetURL(ctx, mediaEntity.ObjectKey)

		// Tạo response item
		mediaResponseList[i] = &media.MediaResponse{
			ID:               mediaEntity.ID,
			MediaType:        string(mediaEntity.MediaType),
			Filename:         mediaEntity.Filename,
			OriginalFilename: mediaEntity.OriginalFilename,
			ContentType:      mediaEntity.ContentType,
			Size:             mediaEntity.Size,
			Status:           string(mediaEntity.Status),
			PublicURL:        url,
			Description:      mediaEntity.Description,
			IsPublic:         mediaEntity.IsPublic,
			Tags:             tags,
			Metadata:         mediaEntity.Metadata,
			Properties:       mediaEntity.Properties,
			CreatedAt:        mediaEntity.CreatedAt,
			UpdatedAt:        mediaEntity.UpdatedAt,
		}
	}

	return mediaResponseList, nextCursor, nil
}

// Update cập nhật thông tin media
func (s *MediaService) Update(ctx context.Context, tenantID string, id string, req *media.UpdateMediaRequest) (*media.MediaResponse, error) {
	// Lấy thông tin hiện tại
	mediaEntity, err := s.mediaRepo.GetByID(ctx, id, tenantID)
	if err != nil {
		return nil, err
	}

	if mediaEntity == nil {
		return nil, fmt.Errorf("media không tồn tại")
	}

	// Cập nhật thông tin
	if req.Description != nil {
		mediaEntity.Description = *req.Description
	}

	if req.IsPublic != nil {
		mediaEntity.IsPublic = *req.IsPublic
	}

	// Lưu thay đổi
	err = s.mediaRepo.Update(ctx, mediaEntity)
	if err != nil {
		return nil, err
	}

	// Cập nhật tags
	if req.Tags != nil {
		// Lấy tags hiện tại
		currentTags, err := s.mediaRepo.GetTags(ctx, id)
		if err != nil {
			return nil, err
		}

		// Tìm tags cần thêm và xóa
		tagsToAdd := make([]string, 0)
		for _, tag := range *req.Tags {
			found := false
			for _, currentTag := range currentTags {
				if tag == currentTag {
					found = true
					break
				}
			}
			if !found {
				tagsToAdd = append(tagsToAdd, tag)
			}
		}

		tagsToRemove := make([]string, 0)
		for _, currentTag := range currentTags {
			found := false
			for _, tag := range *req.Tags {
				if currentTag == tag {
					found = true
					break
				}
			}
			if !found {
				tagsToRemove = append(tagsToRemove, currentTag)
			}
		}

		// Thêm tags mới
		if len(tagsToAdd) > 0 {
			err = s.mediaRepo.AddTags(ctx, id, tagsToAdd)
			if err != nil {
				return nil, err
			}
		}

		// Xóa tags cũ
		if len(tagsToRemove) > 0 {
			err = s.mediaRepo.RemoveTags(ctx, id, tagsToRemove)
			if err != nil {
				return nil, err
			}
		}
	}

	// Lấy thông tin sau khi cập nhật
	return s.Get(ctx, tenantID, id)
}

// Delete xóa media
func (s *MediaService) Delete(ctx context.Context, tenantID string, id string, permanent bool) error {
	// Lấy thông tin media
	mediaEntity, err := s.mediaRepo.GetByID(ctx, id, tenantID)
	if err != nil {
		return err
	}

	if mediaEntity == nil {
		return fmt.Errorf("media không tồn tại")
	}

	// Xóa từ database
	if permanent {
		err = s.mediaRepo.DeletePermanently(ctx, id, tenantID)
	} else {
		err = s.mediaRepo.Delete(ctx, id, tenantID)
	}

	if err != nil {
		return err
	}

	// Nếu xóa vĩnh viễn, xóa cả file từ storage
	if permanent {
		err = s.storageRepo.Delete(ctx, mediaEntity.ObjectKey)
		if err != nil {
			return err
		}
	}

	return nil
}

// GetFile lấy nội dung file của media
func (s *MediaService) GetFile(ctx context.Context, tenantID string, id string) ([]byte, string, error) {
	// Lấy thông tin media
	mediaEntity, err := s.mediaRepo.GetByID(ctx, id, tenantID)
	if err != nil {
		return nil, "", err
	}

	if mediaEntity == nil {
		return nil, "", fmt.Errorf("media không tồn tại")
	}

	// Tải file từ storage
	data, err := s.storageRepo.Download(ctx, mediaEntity.ObjectKey)
	if err != nil {
		return nil, "", err
	}

	return data, mediaEntity.ContentType, nil
}

// ProcessFile xử lý file media (tạo các phiên bản, optimize, v.v.)
func (s *MediaService) ProcessFile(ctx context.Context, mediaEntity *models.Media) error {
	// Hiện tại chỉ cập nhật trạng thái
	mediaEntity.Status = models.MediaStatusReady
	return s.mediaRepo.Update(ctx, mediaEntity)
}

// SaveFile lưu file vào storage
func (s *MediaService) SaveFile(ctx context.Context, tenantID string, file *multipart.FileHeader, mediaType models.MediaType) (*models.Media, error) {
	// Đọc nội dung file
	f, err := file.Open()
	if err != nil {
		fmt.Printf("Lỗi khi mở file: %v\n", err)
		return nil, fmt.Errorf("không thể mở file: %w", err)
	}
	defer f.Close()

	fileData, err := ioutil.ReadAll(f)
	if err != nil {
		fmt.Printf("Lỗi khi đọc file: %v\n", err)
		return nil, fmt.Errorf("không thể đọc nội dung file: %w", err)
	}

	// Tạo key cho object storage
	fileExt := filepath.Ext(file.Filename)
	objectKey := fmt.Sprintf(
		"%s/%s/%s/%s%s",
		tenantID,
		string(mediaType),
		time.Now().Format("2006-01-02"),
		uuid.New().String(),
		fileExt,
	)

	// Thêm log để debug
	fmt.Printf("Uploading file to Minio: %s, Size: %d bytes, Type: %s\n",
		objectKey, len(fileData), file.Header.Get("Content-Type"))

	// Thử upload tối đa 3 lần
	maxRetries := 3
	var uploadErr error

	for i := 0; i < maxRetries; i++ {
		uploadErr = s.storageRepo.Upload(ctx, fileData, objectKey, file.Header.Get("Content-Type"))
		if uploadErr == nil {
			break
		}
		fmt.Printf("Thử upload lần %d thất bại: %v\n", i+1, uploadErr)
		time.Sleep(time.Second * time.Duration(i+1)) // Backoff nhẹ
	}

	if uploadErr != nil {
		fmt.Printf("Lỗi khi upload file lên Minio sau %d lần thử: %v\n", maxRetries, uploadErr)
		return nil, fmt.Errorf("không thể upload file lên storage: %w", uploadErr)
	}

	fmt.Printf("Upload thành công file: %s\n", objectKey)

	// Tạo entity mới
	media := &models.Media{
		ID:               uuid.New().String(),
		TenantID:         tenantID,
		MediaType:        mediaType,
		Filename:         file.Filename,
		OriginalFilename: file.Filename,
		ObjectKey:        objectKey,
		ContentType:      file.Header.Get("Content-Type"),
		Size:             file.Size,
		Status:           models.MediaStatusPending,
		SchemaVersion:    1,
		Metadata:         make(models.Metadata),
		Properties:       make(models.Properties),
	}

	// Thêm metadata cơ bản
	media.Metadata["originalSize"] = file.Size

	return media, nil
}

// Hàm hỗ trợ xác định loại media từ content type
func getMediaTypeFromContentType(contentType string) models.MediaType {
	contentType = strings.ToLower(contentType)

	if strings.HasPrefix(contentType, "image/") {
		return models.MediaTypeImage
	}

	if strings.HasPrefix(contentType, "video/") {
		return models.MediaTypeVideo
	}

	if strings.HasPrefix(contentType, "audio/") {
		return models.MediaTypeAudio
	}

	if contentType == "application/pdf" ||
		contentType == "application/msword" ||
		contentType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document" {
		return models.MediaTypeDocument
	}

	return models.MediaTypeOther
}
