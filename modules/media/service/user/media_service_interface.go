package user

import (
	"context"
	"mime/multipart"

	"github.com/webnew/wn-backend-v2/modules/media/dto/media"
	"github.com/webnew/wn-backend-v2/modules/media/models"
)

// MediaServiceInterface defines the interface for media service
type MediaServiceInterface interface {
	Upload(ctx context.Context, tenantID string, userID string, req *media.UploadMediaRequest) (*media.UploadMediaResponse, error)
	Get(ctx context.Context, tenantID string, id string) (*media.MediaResponse, error)
	List(ctx context.Context, tenantID string, req *media.ListMediaRequest) ([]*media.MediaResponse, string, error)
	Update(ctx context.Context, tenantID string, id string, req *media.UpdateMediaRequest) (*media.MediaResponse, error)
	Delete(ctx context.Context, tenantID string, id string, permanent bool) error
	GetFile(ctx context.Context, tenantID string, id string) ([]byte, string, error)
	ProcessFile(ctx context.Context, mediaEntity *models.Media) error
	SaveFile(ctx context.Context, tenantID string, file *multipart.FileHeader, mediaType models.MediaType) (*models.Media, error)
}

// Ensure MediaService implements MediaServiceInterface
var _ MediaServiceInterface = (*MediaService)(nil)

// Ensure TracedMediaService implements MediaServiceInterface
var _ MediaServiceInterface = (*TracedMediaService)(nil)
