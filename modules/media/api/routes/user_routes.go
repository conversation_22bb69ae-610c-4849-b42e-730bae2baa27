package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/media/api/handlers/user"
	"github.com/webnew/wn-backend-v2/modules/media/repository"
	"github.com/webnew/wn-backend-v2/modules/media/service/common"
	userService "github.com/webnew/wn-backend-v2/modules/media/service/user"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	pkgMiddleware "github.com/webnew/wn-backend-v2/pkg/middleware"
)

// RegisterUserRoutes đăng ký các routes cho API media user
func RegisterUserRoutes(
	router *gin.RouterGroup,
	mediaService common.MediaService,
	mediaRepo repository.MediaRepository,
	folderRepo repository.MediaFolderRepository,
	storageRepo repository.StorageRepository,
	jwtService *auth.JWTService,
	permService pkgMiddleware.PermissionService,
) {
	// Chuyển đổi từ common.MediaService sang *user.MediaService
	mediaUserService, ok := mediaService.(*userService.MediaService)
	if !ok {
		mediaUserService = userService.NewMediaService(mediaRepo, storageRepo)
	}

	// Khởi tạo service cho thư mục
	folderUserService := userService.NewMediaFolderService(folderRepo)

	// Khởi tạo handlers
	mediaHandler := user.NewMediaHandler(mediaUserService, jwtService)
	folderHandler := user.NewMediaFolderHandler(folderUserService, jwtService)

	mediaRoutes := router.Group("/media/file")
	{
		// Sử dụng JWT middleware cho tất cả các routes
		mediaRoutes.Use(jwtService.JWTAuthMiddleware())

		// Upload media mới
		mediaRoutes.POST("", pkgMiddleware.RequirePermission(permService, "media.upload"), mediaHandler.UploadFile)

		// Lấy danh sách media
		mediaRoutes.GET("", pkgMiddleware.RequirePermission(permService, "media.read"), mediaHandler.GetMediaList)

		// Lấy thông tin chi tiết về media
		mediaRoutes.GET("/:id", pkgMiddleware.RequirePermission(permService, "media.read"), mediaHandler.GetMediaByID)

		// Lấy URL của media
		mediaRoutes.GET("/:id/url", pkgMiddleware.RequirePermission(permService, "media.download"), mediaHandler.GetMediaURL)

		// Cập nhật thông tin media
		mediaRoutes.PATCH("/:id", pkgMiddleware.RequirePermission(permService, "media.update"), mediaHandler.UpdateMedia)

		// Xóa media
		mediaRoutes.DELETE("/:id", pkgMiddleware.RequirePermission(permService, "media.delete"), mediaHandler.DeleteMedia)
	}

	folderRoutes := router.Group("/media/folder")
	{
		// Sử dụng JWT middleware cho tất cả các routes
		folderRoutes.Use(jwtService.JWTAuthMiddleware())

		// Tạo thư mục mới
		folderRoutes.POST("", pkgMiddleware.RequirePermission(permService, "media.folders.create"), folderHandler.CreateFolder)

		// Lấy danh sách thư mục
		folderRoutes.GET("", pkgMiddleware.RequirePermission(permService, "media.folders.read"), folderHandler.GetFolderList)

		// Lấy thông tin chi tiết về thư mục
		folderRoutes.GET("/:id", pkgMiddleware.RequirePermission(permService, "media.folders.read"), folderHandler.GetFolderByID)

		// Cập nhật thông tin thư mục
		folderRoutes.PATCH("/:id", pkgMiddleware.RequirePermission(permService, "media.folders.update"), folderHandler.UpdateFolder)

		// Xóa thư mục
		folderRoutes.DELETE("/:id", pkgMiddleware.RequirePermission(permService, "media.folders.delete"), folderHandler.DeleteFolder)
	}
}
