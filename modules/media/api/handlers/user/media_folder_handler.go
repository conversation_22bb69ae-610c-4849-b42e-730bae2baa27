package user

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/media/dto/folder"
	userService "github.com/webnew/wn-backend-v2/modules/media/service/user"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	"github.com/webnew/wn-backend-v2/pkg/response"
)

// MediaFolderHandler xử lý các yêu cầu HTTP liên quan đến Media Folder
type MediaFolderHandler struct {
	folderService *userService.MediaFolderService
	jwtService    *auth.JWTService
}

// NewMediaFolderHandler tạo một instance mới của MediaFolderHandler
func NewMediaFolderHandler(folderService *userService.MediaFolderService, jwtService *auth.JWTService) *MediaFolderHandler {
	return &MediaFolderHandler{
		folderService: folderService,
		jwtService:    jwtService,
	}
}

// CreateFolder xử lý yêu cầu tạo thư mục mới
func (h *MediaFolderHandler) CreateFolder(c *gin.Context) {
	// Parse request body
	var req folder.CreateFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không thể xác thực tenant", "UNAUTHORIZED", details)
		return
	}

	// Lấy user ID từ context
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		details := []interface{}{map[string]string{"message": "Không tìm thấy thông tin người dùng"}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không thể xác thực người dùng", "UNAUTHORIZED", details)
		return
	}

	// Gọi service để tạo thư mục mới
	folderRes, err := h.folderService.Create(c.Request.Context(), int(tenantID), int(userID), &req)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi khi tạo thư mục", "CREATE_FOLDER_ERROR", details)
		return
	}

	// Trả về response
	response.Success(c, http.StatusCreated, "Tạo thư mục thành công", folderRes)
}

// GetFolderList xử lý yêu cầu lấy danh sách thư mục
func (h *MediaFolderHandler) GetFolderList(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không thể xác thực tenant", "UNAUTHORIZED", details)
		return
	}

	// Parse query parameters
	var req folder.ListFolderRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Tham số không hợp lệ", "INVALID_PARAMETERS", details)
		return
	}

	// Gọi service để lấy danh sách
	folders, nextCursor, err := h.folderService.List(c.Request.Context(), int(tenantID), &req)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách thư mục", "LIST_FOLDER_ERROR", details)
		return
	}

	// Tạo meta cho pagination
	meta := map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    nextCursor != "",
	}

	// Trả về response
	response.SuccessWithMeta(c, http.StatusOK, "Lấy danh sách thư mục thành công", folders, meta)
}

// GetFolderByID xử lý yêu cầu lấy thông tin chi tiết về một thư mục
func (h *MediaFolderHandler) GetFolderByID(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không thể xác thực tenant", "UNAUTHORIZED", details)
		return
	}

	// Lấy ID từ path
	id := c.Param("id")
	folderID, err := strconv.Atoi(id)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "ID thư mục không hợp lệ", "INVALID_ID", details)
		return
	}

	// Gọi service để lấy thông tin
	folderRes, err := h.folderService.Get(c.Request.Context(), int(tenantID), folderID)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi khi lấy thông tin thư mục", "GET_FOLDER_ERROR", details)
		return
	}

	if folderRes == nil {
		details := []interface{}{map[string]string{"message": "Thư mục không tồn tại"}}
		response.ErrorWithDetails(c, http.StatusNotFound, "Không tìm thấy thư mục", "FOLDER_NOT_FOUND", details)
		return
	}

	// Trả về response
	response.Success(c, http.StatusOK, "Lấy thông tin thư mục thành công", folderRes)
}

// UpdateFolder xử lý yêu cầu cập nhật thông tin thư mục
func (h *MediaFolderHandler) UpdateFolder(c *gin.Context) {
	// Parse request body
	var req folder.UpdateFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không thể xác thực tenant", "UNAUTHORIZED", details)
		return
	}

	// Lấy user ID từ context
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		details := []interface{}{map[string]string{"message": "Không tìm thấy thông tin người dùng"}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không thể xác thực người dùng", "UNAUTHORIZED", details)
		return
	}

	// Lấy ID từ path
	id := c.Param("id")
	folderID, err := strconv.Atoi(id)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "ID thư mục không hợp lệ", "INVALID_ID", details)
		return
	}

	// Gọi service để cập nhật
	folderRes, err := h.folderService.Update(c.Request.Context(), int(tenantID), int(userID), folderID, &req)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi khi cập nhật thư mục", "UPDATE_FOLDER_ERROR", details)
		return
	}

	// Trả về response
	response.Success(c, http.StatusOK, "Cập nhật thư mục thành công", folderRes)
}

// DeleteFolder xử lý yêu cầu xóa thư mục
func (h *MediaFolderHandler) DeleteFolder(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không thể xác thực tenant", "UNAUTHORIZED", details)
		return
	}

	// Lấy ID từ path
	id := c.Param("id")
	folderID, err := strconv.Atoi(id)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "ID thư mục không hợp lệ", "INVALID_ID", details)
		return
	}

	// Gọi service để xóa
	err = h.folderService.Delete(c.Request.Context(), int(tenantID), folderID)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi khi xóa thư mục", "DELETE_FOLDER_ERROR", details)
		return
	}

	// Trả về response
	response.Success(c, http.StatusOK, "Xóa thư mục thành công", nil)
}
