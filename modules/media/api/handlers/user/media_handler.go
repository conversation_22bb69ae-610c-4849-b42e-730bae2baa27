package user

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/pkg/response"

	"github.com/webnew/wn-backend-v2/modules/media/dto/common"
	"github.com/webnew/wn-backend-v2/modules/media/dto/media"
	service "github.com/webnew/wn-backend-v2/modules/media/service/user"
	"github.com/webnew/wn-backend-v2/pkg/auth"
)

// MediaHandler xử lý HTTP request liên quan đến media cho user
type MediaHandler struct {
	mediaService *service.MediaService
	jwtService   *auth.JWTService
}

// NewMediaHandler tạo một instance mới của MediaHandler
func NewMediaHandler(mediaService *service.MediaService, jwtService *auth.JWTService) *MediaHandler {
	return &MediaHandler{
		mediaService: mediaService,
		jwtService:   jwtService,
	}
}

// UploadFile xử lý request upload media mới
func (h *MediaHandler) UploadFile(c *gin.Context) {
	// Đọc thông tin từ form
	file, err := c.FormFile("file")
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "File không hợp lệ", "INVALID_FILE", details)
		return
	}

	// Đọc các tham số khác
	// Chú ý: các biến này không được sử dụng trong cài đặt mới
	// nhưng có thể cần trong tương lai
	_ = c.DefaultPostForm("folder", "default")
	_ = c.DefaultPostForm("title", "")
	description := c.DefaultPostForm("description", "")

	// Kiểm tra và đọc file
	f, err := file.Open()
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Không thể đọc file", "FILE_READ_ERROR", details)
		return
	}
	defer f.Close()

	// Đọc dữ liệu file
	fileSize := file.Size
	if fileSize > 10*1024*1024 {
		details := []interface{}{map[string]string{"message": fmt.Sprintf("Kích thước file %d vượt quá giới hạn", fileSize)}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Kích thước file quá lớn (tối đa 10MB)", "FILE_TOO_LARGE", details)
		return
	}

	// Tạo buffer để đọc file
	buffer := make([]byte, fileSize)
	_, err = f.Read(buffer)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Không thể đọc dữ liệu file", "FILE_READ_ERROR", details)
		return
	}

	// Lấy thông tin người dùng từ context
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		details := []interface{}{map[string]string{"message": "Không tìm thấy thông tin người dùng"}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng", "UNAUTHORIZED", details)
		return
	}

	// Tạo thông tin media
	uploadReq := &media.UploadMediaRequest{
		File:        file,
		Description: description,
		Tags:        []string{},
		IsPublic:    true,
	}

	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", "UNAUTHORIZED", details)
		return
	}

	// Upload media
	result, err := h.mediaService.Upload(c, strconv.Itoa(int(tenantID)), strconv.Itoa(int(userID)), uploadReq)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Không thể tải lên file", "UPLOAD_FAILED", details)
		return
	}

	response.Success(c, http.StatusCreated, "Tải lên file thành công", result)
}

// GetMediaByID xử lý request lấy thông tin chi tiết về media
func (h *MediaHandler) GetMediaByID(c *gin.Context) {
	// Lấy ID từ parameter
	id := c.Param("id")
	if id == "" {
		details := []interface{}{map[string]string{"message": "ID không được để trống"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "ID không được để trống", "MISSING_ID", details)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", "UNAUTHORIZED", details)
		return
	}

	// Lấy thông tin media
	media, err := h.mediaService.Get(c, strconv.Itoa(int(tenantID)), id)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Không thể lấy thông tin media", "MEDIA_FETCH_ERROR", details)
		return
	}

	response.Success(c, http.StatusOK, "Lấy thông tin media thành công", media)
}

// GetMediaURL xử lý request lấy URL của media
func (h *MediaHandler) GetMediaURL(c *gin.Context) {
	// Lấy ID từ parameter
	id := c.Param("id")
	if id == "" {
		details := []interface{}{map[string]string{"message": "ID không được để trống"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "ID không được để trống", "MISSING_ID", details)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", "UNAUTHORIZED", details)
		return
	}

	// Lấy thông tin media
	mediaInfo, err := h.mediaService.Get(c, strconv.Itoa(int(tenantID)), id)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Không thể lấy thông tin media", "MEDIA_FETCH_ERROR", details)
		return
	}

	response.Success(c, http.StatusOK, "Lấy URL media thành công", gin.H{"url": mediaInfo.PublicURL})
}

// UpdateMedia xử lý request cập nhật thông tin media
func (h *MediaHandler) UpdateMedia(c *gin.Context) {
	// Lấy ID từ parameter
	id := c.Param("id")
	if id == "" {
		details := []interface{}{map[string]string{"message": "ID không được để trống"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "ID không được để trống", "MISSING_ID", details)
		return
	}

	// Parse request body
	var req media.UpdateMediaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", "UNAUTHORIZED", details)
		return
	}

	// Cập nhật media
	updatedMedia, err := h.mediaService.Update(c, strconv.Itoa(int(tenantID)), id, &req)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Không thể cập nhật media", "UPDATE_FAILED", details)
		return
	}

	response.Success(c, http.StatusOK, "Cập nhật media thành công", updatedMedia)
}

// DeleteMedia xử lý request xóa media
func (h *MediaHandler) DeleteMedia(c *gin.Context) {
	// Lấy ID từ parameter
	id := c.Param("id")
	if id == "" {
		details := []interface{}{map[string]string{"message": "ID không được để trống"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "ID không được để trống", "MISSING_ID", details)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", "UNAUTHORIZED", details)
		return
	}

	// Xóa media
	err = h.mediaService.Delete(c, strconv.Itoa(int(tenantID)), id, false)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Không thể xóa media", "DELETE_FAILED", details)
		return
	}

	response.Success(c, http.StatusOK, "Xóa media thành công", nil)
}

// GetMediaList xử lý request lấy danh sách media
func (h *MediaHandler) GetMediaList(c *gin.Context) {
	// Lấy tham số tìm kiếm từ query
	title := c.Query("title")
	// Các tham số không sử dụng trong triển khai mới
	// nhưng giữ lại để tương thích với code cũ
	_ = c.Query("folder")
	_ = c.Query("owner_id")
	_ = c.Query("owner_type")
	_ = c.Query("mime_type")
	_ = c.Query("extension")

	// Parse các tham số phân trang
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Lấy tenant ID từ context
	tenantID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", "UNAUTHORIZED", details)
		return
	}

	// Tạo request list
	listReq := &media.ListMediaRequest{
		CursorPaginationRequest: common.CursorPaginationRequest{
			Cursor: c.Query("cursor"),
			Limit:  pageSize,
		},
		MediaType: c.Query("media_type"),
		Status:    c.Query("status"),
		IsPublic:  nil,
		Tags:      []string{},
		Search:    title,
	}

	// Parse các tham số bool
	if isPublicStr := c.Query("is_public"); isPublicStr != "" {
		isPublic := isPublicStr == "true"
		listReq.IsPublic = &isPublic
	}

	// Parse các tham số array
	if tagsStr := c.Query("tags"); tagsStr != "" {
		listReq.Tags = strings.Split(tagsStr, ",")
	}

	// Lấy danh sách media
	results, nextCursor, err := h.mediaService.List(c, strconv.Itoa(int(tenantID)), listReq)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Không thể lấy danh sách media", "LIST_FAILED", details)
		return
	}

	// Tạo response
	response.SuccessWithMeta(c, http.StatusOK, "Lấy danh sách media thành công", results, map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    nextCursor != "",
	})
}

// UploadImage là API endpoint tối ưu cho việc upload ảnh với resize
func (h *MediaHandler) UploadImage(c *gin.Context) {
	details := []interface{}{map[string]string{"message": "Chức năng này chưa được triển khai"}}
	response.ErrorWithDetails(c, http.StatusNotImplemented, "Chức năng này chưa được triển khai", "NOT_IMPLEMENTED", details)
}

// BatchUpload là API endpoint cho việc upload nhiều file cùng lúc
func (h *MediaHandler) BatchUpload(c *gin.Context) {
	details := []interface{}{map[string]string{"message": "Chức năng này chưa được triển khai"}}
	response.ErrorWithDetails(c, http.StatusNotImplemented, "Chức năng này chưa được triển khai", "NOT_IMPLEMENTED", details)
}
