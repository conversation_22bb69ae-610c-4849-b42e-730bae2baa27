package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/media/models"
	"github.com/webnew/wn-backend-v2/modules/media/repository"
)

// MediaFolderRepository triển khai repository.MediaFolderRepository interface
type MediaFolderRepository struct {
	db *sqlx.DB
}

// NewMediaFolderRepository tạo một instance mới của MediaFolderRepository
func NewMediaFolderRepository(db *sqlx.DB) *MediaFolderRepository {
	return &MediaFolderRepository{
		db: db,
	}
}

// Create tạo một thư mục mới
func (r *MediaFolderRepository) Create(ctx context.Context, folder *models.MediaFolder) error {
	now := time.Now()
	folder.CreatedAt = now
	folder.UpdatedAt = now

	query := `
	INSERT INTO media_folders (
		tenant_id, parent_id, name, slug, description, 
		is_public, created_at, updated_at, created_by, updated_by
	) VALUES (
		:tenant_id, :parent_id, :name, :slug, :description, 
		:is_public, :created_at, :updated_at, :created_by, :updated_by
	)
	`

	result, err := r.db.NamedExecContext(ctx, query, folder)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	folder.FolderID = int(id)
	return nil
}

// GetByID lấy thư mục theo ID
func (r *MediaFolderRepository) GetByID(ctx context.Context, folderID int, tenantID int) (*models.MediaFolder, error) {
	query := `
	SELECT * FROM media_folders 
	WHERE folder_id = ? AND tenant_id = ? AND deleted_at IS NULL
	`

	var folder models.MediaFolder
	err := r.db.GetContext(ctx, &folder, query, folderID, tenantID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &folder, nil
}

// GetBySlug lấy thư mục theo slug
func (r *MediaFolderRepository) GetBySlug(ctx context.Context, slug string, tenantID int) (*models.MediaFolder, error) {
	query := `
	SELECT * FROM media_folders 
	WHERE slug = ? AND tenant_id = ? AND deleted_at IS NULL
	`

	var folder models.MediaFolder
	err := r.db.GetContext(ctx, &folder, query, slug, tenantID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &folder, nil
}

// Update cập nhật thông tin thư mục
func (r *MediaFolderRepository) Update(ctx context.Context, folder *models.MediaFolder) error {
	folder.UpdatedAt = time.Now()

	query := `
	UPDATE media_folders SET
		parent_id = :parent_id,
		name = :name,
		slug = :slug,
		description = :description,
		is_public = :is_public,
		updated_at = :updated_at,
		updated_by = :updated_by
	WHERE folder_id = :folder_id AND tenant_id = :tenant_id AND deleted_at IS NULL
	`

	_, err := r.db.NamedExecContext(ctx, query, folder)
	return err
}

// Delete xóa thư mục (soft delete)
func (r *MediaFolderRepository) Delete(ctx context.Context, folderID int, tenantID int) error {
	now := time.Now()

	query := `
	UPDATE media_folders SET
		deleted_at = ?
	WHERE folder_id = ? AND tenant_id = ? AND deleted_at IS NULL
	`

	_, err := r.db.ExecContext(ctx, query, now, folderID, tenantID)
	return err
}

// List lấy danh sách thư mục con
func (r *MediaFolderRepository) List(ctx context.Context, params repository.ListMediaFolderParams) ([]*models.MediaFolder, string, error) {
	whereConditions := []string{"deleted_at IS NULL"}
	args := []interface{}{}

	// Thêm điều kiện tenant_id
	whereConditions = append(whereConditions, "tenant_id = ?")
	args = append(args, params.TenantID)

	// Thêm điều kiện parent_id
	if params.ParentID != nil {
		whereConditions = append(whereConditions, "parent_id = ?")
		args = append(args, *params.ParentID)
	} else {
		whereConditions = append(whereConditions, "parent_id IS NULL")
	}

	// Thêm điều kiện is_public
	if params.IsPublic != nil {
		whereConditions = append(whereConditions, "is_public = ?")
		args = append(args, *params.IsPublic)
	}

	// Thêm điều kiện tìm kiếm theo name hoặc description
	if params.Search != "" {
		whereConditions = append(whereConditions, "(name LIKE ? OR description LIKE ?)")
		searchTerm := "%" + params.Search + "%"
		args = append(args, searchTerm, searchTerm)
	}

	// Phân trang bằng cursor
	if params.Cursor != "" {
		whereConditions = append(whereConditions, "folder_id > ?")
		args = append(args, params.Cursor)
	}

	// Tạo câu truy vấn
	query := fmt.Sprintf(`
	SELECT * FROM media_folders
	WHERE %s
	ORDER BY folder_id ASC
	LIMIT ?
	`, strings.Join(whereConditions, " AND "))

	// Thêm limit
	limit := 10
	if params.Limit > 0 {
		limit = params.Limit
	}
	args = append(args, limit)

	// Thực hiện truy vấn
	var folderList []*models.MediaFolder
	err := r.db.SelectContext(ctx, &folderList, query, args...)
	if err != nil {
		return nil, "", err
	}

	// Lấy cursor tiếp theo
	var nextCursor string
	if len(folderList) == limit {
		nextCursor = fmt.Sprintf("%d", folderList[len(folderList)-1].FolderID)
	}

	return folderList, nextCursor, nil
}
