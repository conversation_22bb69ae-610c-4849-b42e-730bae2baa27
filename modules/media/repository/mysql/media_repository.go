package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/media/models"
	"github.com/webnew/wn-backend-v2/modules/media/repository"
)

// MediaRepository triển khai repository.MediaRepository interface
type MediaRepository struct {
	db *sqlx.DB
}

// NewMediaRepository tạo một instance mới của MediaRepository
func NewMediaRepository(db *sqlx.DB) *MediaRepository {
	return &MediaRepository{
		db: db,
	}
}

// Create tạo một media mới
func (r *MediaRepository) Create(ctx context.Context, media *models.Media) error {
	if media.ID == "" {
		media.ID = uuid.New().String()
	}

	now := time.Now()
	media.CreatedAt = now
	media.UpdatedAt = now

	// Mặc định status là pending nếu không được đặt
	if media.Status == "" {
		media.Status = models.MediaStatusPending
	}

	// Mặc định schema version là 1 nếu không được đặt
	if media.SchemaVersion == 0 {
		media.SchemaVersion = 1
	}

	query := `
	INSERT INTO media (
		id, tenant_id, media_type, filename, original_filename, 
		object_key, content_type, size, status, public_url, 
		description, uploaded_by, checksum, is_public, metadata, 
		properties, schema_version, created_at, updated_at, deleted_at
	) VALUES (
		:id, :tenant_id, :media_type, :filename, :original_filename, 
		:object_key, :content_type, :size, :status, :public_url, 
		:description, :uploaded_by, :checksum, :is_public, :metadata, 
		:properties, :schema_version, :created_at, :updated_at, :deleted_at
	)
	`

	_, err := r.db.NamedExecContext(ctx, query, media)
	return err
}

// GetByID lấy media theo ID
func (r *MediaRepository) GetByID(ctx context.Context, id string, tenantID string) (*models.Media, error) {
	query := `
	SELECT * FROM media 
	WHERE id = ? AND tenant_id = ? AND deleted_at IS NULL
	`

	var media models.Media
	err := r.db.GetContext(ctx, &media, query, id, tenantID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &media, nil
}

// Update cập nhật thông tin media
func (r *MediaRepository) Update(ctx context.Context, media *models.Media) error {
	media.UpdatedAt = time.Now()

	query := `
	UPDATE media SET
		media_type = :media_type,
		filename = :filename,
		status = :status,
		public_url = :public_url,
		description = :description,
		is_public = :is_public,
		metadata = :metadata,
		properties = :properties,
		updated_at = :updated_at
	WHERE id = :id AND tenant_id = :tenant_id AND deleted_at IS NULL
	`

	_, err := r.db.NamedExecContext(ctx, query, media)
	return err
}

// Delete xóa media (soft delete)
func (r *MediaRepository) Delete(ctx context.Context, id string, tenantID string) error {
	now := time.Now()

	query := `
	UPDATE media SET
		deleted_at = ?
	WHERE id = ? AND tenant_id = ? AND deleted_at IS NULL
	`

	_, err := r.db.ExecContext(ctx, query, now, id, tenantID)
	return err
}

// DeletePermanently xóa media vĩnh viễn
func (r *MediaRepository) DeletePermanently(ctx context.Context, id string, tenantID string) error {
	query := `
	DELETE FROM media
	WHERE id = ? AND tenant_id = ?
	`

	_, err := r.db.ExecContext(ctx, query, id, tenantID)
	return err
}

// List lấy danh sách media với phân trang
func (r *MediaRepository) List(ctx context.Context, params repository.ListMediaParams) ([]*models.Media, string, error) {
	whereConditions := []string{"deleted_at IS NULL"}
	args := []interface{}{}

	// Thêm điều kiện tenant_id
	if params.TenantID != "" {
		whereConditions = append(whereConditions, "tenant_id = ?")
		args = append(args, params.TenantID)
	}

	// Thêm điều kiện media_type
	if params.MediaType != "" {
		whereConditions = append(whereConditions, "media_type = ?")
		args = append(args, params.MediaType)
	}

	// Thêm điều kiện status
	if params.Status != "" {
		whereConditions = append(whereConditions, "status = ?")
		args = append(args, params.Status)
	}

	// Thêm điều kiện is_public
	if params.IsPublic != nil {
		whereConditions = append(whereConditions, "is_public = ?")
		args = append(args, *params.IsPublic)
	}

	// Thêm điều kiện tìm kiếm theo filename hoặc description
	if params.Search != "" {
		whereConditions = append(whereConditions, "(filename LIKE ? OR description LIKE ?)")
		searchTerm := "%" + params.Search + "%"
		args = append(args, searchTerm, searchTerm)
	}

	// Phân trang bằng cursor
	if params.Cursor != "" {
		whereConditions = append(whereConditions, "id > ?")
		args = append(args, params.Cursor)
	}

	// Tạo câu truy vấn
	query := fmt.Sprintf(`
	SELECT * FROM media
	WHERE %s
	ORDER BY id ASC
	LIMIT ?
	`, strings.Join(whereConditions, " AND "))

	// Thêm limit
	limit := 10
	if params.Limit > 0 {
		limit = params.Limit
	}
	args = append(args, limit)

	// Thực hiện truy vấn
	var mediaList []*models.Media
	err := r.db.SelectContext(ctx, &mediaList, query, args...)
	if err != nil {
		return nil, "", err
	}

	// Lấy cursor tiếp theo
	var nextCursor string
	if len(mediaList) == limit {
		nextCursor = mediaList[len(mediaList)-1].ID
	}

	// Xử lý truy vấn tags nếu cần
	if len(params.Tags) > 0 {
		filteredList := make([]*models.Media, 0, len(mediaList))

		for _, media := range mediaList {
			// Lấy tags của media
			tags, err := r.GetTags(ctx, media.ID)
			if err != nil {
				return nil, "", err
			}

			// Kiểm tra xem media có chứa tất cả các tags trong params.Tags hay không
			if containsAllTags(tags, params.Tags) {
				filteredList = append(filteredList, media)
			}
		}

		mediaList = filteredList
	}

	return mediaList, nextCursor, nil
}

// AddTags thêm tags cho media
func (r *MediaRepository) AddTags(ctx context.Context, mediaID string, tags []string) error {
	if len(tags) == 0 {
		return nil
	}

	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return err
	}

	// Rollback transaction nếu có lỗi
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	for _, tag := range tags {
		// Kiểm tra tag đã tồn tại chưa
		var count int
		err = tx.GetContext(ctx, &count, "SELECT COUNT(*) FROM media_tags WHERE media_id = ? AND tag = ?", mediaID, tag)
		if err != nil {
			return err
		}

		// Nếu tag chưa tồn tại, thêm vào
		if count == 0 {
			_, err = tx.ExecContext(ctx, "INSERT INTO media_tags (media_id, tag) VALUES (?, ?)", mediaID, tag)
			if err != nil {
				return err
			}
		}
	}

	return tx.Commit()
}

// RemoveTags xóa tags khỏi media
func (r *MediaRepository) RemoveTags(ctx context.Context, mediaID string, tags []string) error {
	if len(tags) == 0 {
		return nil
	}

	placeholders := make([]string, len(tags))
	args := make([]interface{}, len(tags)+1)

	args[0] = mediaID
	for i, tag := range tags {
		placeholders[i] = "?"
		args[i+1] = tag
	}

	query := fmt.Sprintf(
		"DELETE FROM media_tags WHERE media_id = ? AND tag IN (%s)",
		strings.Join(placeholders, ", "),
	)

	_, err := r.db.ExecContext(ctx, query, args...)
	return err
}

// GetTags lấy tất cả tags của media
func (r *MediaRepository) GetTags(ctx context.Context, mediaID string) ([]string, error) {
	query := "SELECT tag FROM media_tags WHERE media_id = ?"

	var tags []string
	err := r.db.SelectContext(ctx, &tags, query, mediaID)
	if err != nil {
		return nil, err
	}

	return tags, nil
}

// containsAllTags kiểm tra xem slice a có chứa tất cả các phần tử của slice b hay không
func containsAllTags(a, b []string) bool {
	if len(b) == 0 {
		return true
	}

	tagMap := make(map[string]bool)
	for _, tag := range a {
		tagMap[tag] = true
	}

	for _, tag := range b {
		if !tagMap[tag] {
			return false
		}
	}

	return true
}
