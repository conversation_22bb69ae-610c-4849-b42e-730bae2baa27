package s3

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/webnew/wn-backend-v2/modules/media/configs"
	"github.com/webnew/wn-backend-v2/modules/media/repository"
)

// Cấu hình MinioStorage
type MinioConfig struct {
	Endpoint        string        // Địa chỉ endpoint của Minio/S3
	AccessKeyID     string        // Key ID truy cập
	SecretAccessKey string        // Secret key truy cập
	UseSSL          bool          // Sử dụng SSL hay không
	BucketName      string        // Tên bucket lưu trữ media
	Region          string        // Khu vực của bucket
	PresignedExpiry time.Duration // Thời gian hết hạn của presigned URL
}

// MinioStorage implementation của repository.StorageRepository
type MinioStorage struct {
	client     *minio.Client
	config     MinioConfig
	bucketName string
}

// NewMinioStorage tạo một instance mới của MinioStorage
func NewMinioStorage(config configs.MinioStorage) (*MinioStorage, error) {
	// Chuyển đổi từ configs.MinioStorage sang MinioConfig
	minioConfig := MinioConfig{
		Endpoint:        config.Endpoint,
		AccessKeyID:     config.AccessKeyID,
		SecretAccessKey: config.SecretAccessKey,
		UseSSL:          config.UseSSL,
		BucketName:      config.BucketName,
		Region:          config.Region,
		PresignedExpiry: config.PresignedExpiry,
	}

	// Kiểm tra các trường bắt buộc
	if minioConfig.Endpoint == "" {
		return nil, fmt.Errorf("endpoint is required")
	}
	if minioConfig.AccessKeyID == "" {
		return nil, fmt.Errorf("access key ID is required")
	}
	if minioConfig.SecretAccessKey == "" {
		return nil, fmt.Errorf("secret access key is required")
	}
	if minioConfig.BucketName == "" {
		return nil, fmt.Errorf("bucket name is required")
	}

	// Thiết lập thời gian mặc định cho presigned URL nếu không được cung cấp
	if minioConfig.PresignedExpiry == 0 {
		minioConfig.PresignedExpiry = 15 * time.Minute
	}

	// Khởi tạo client Minio
	minioClient, err := minio.New(minioConfig.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(minioConfig.AccessKeyID, minioConfig.SecretAccessKey, ""),
		Secure: minioConfig.UseSSL,
		Region: minioConfig.Region,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create minio client: %w", err)
	}

	// Kiểm tra xem bucket có tồn tại không
	exists, err := minioClient.BucketExists(context.Background(), minioConfig.BucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to check if bucket exists: %w", err)
	}

	// Tạo bucket nếu không tồn tại
	if !exists {
		err = minioClient.MakeBucket(context.Background(), minioConfig.BucketName, minio.MakeBucketOptions{
			Region: minioConfig.Region,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to create bucket: %w", err)
		}

		// Thiết lập chính sách public read cho bucket
		policy := `{
			"Version": "2012-10-17",
			"Statement": [
				{
					"Effect": "Allow",
					"Principal": "*",
					"Action": "s3:GetObject",
					"Resource": "arn:aws:s3:::%s/*"
				}
			]
		}`
		policy = fmt.Sprintf(policy, minioConfig.BucketName)

		// Áp dụng chính sách
		err = minioClient.SetBucketPolicy(context.Background(), minioConfig.BucketName, policy)
		if err != nil {
			return nil, fmt.Errorf("failed to set bucket policy: %w", err)
		}
	} else {
		// Nếu bucket đã tồn tại, vẫn cần cập nhật policy để đảm bảo truy cập public
		policy := `{
			"Version": "2012-10-17",
			"Statement": [
				{
					"Effect": "Allow",
					"Principal": "*",
					"Action": "s3:GetObject",
					"Resource": "arn:aws:s3:::%s/*"
				}
			]
		}`
		policy = fmt.Sprintf(policy, minioConfig.BucketName)

		// Áp dụng chính sách
		err = minioClient.SetBucketPolicy(context.Background(), minioConfig.BucketName, policy)
		if err != nil {
			return nil, fmt.Errorf("failed to set bucket policy: %w", err)
		}
	}

	return &MinioStorage{
		client:     minioClient,
		config:     minioConfig,
		bucketName: minioConfig.BucketName,
	}, nil
}

// Upload triển khai phương thức Upload của interface StorageRepository
func (s *MinioStorage) Upload(ctx context.Context, fileData []byte, objectKey string, contentType string) error {
	fmt.Printf("### MinioStorage.Upload START: objectKey=%s, contentType=%s, dataSize=%d bytes\n",
		objectKey, contentType, len(fileData))

	reader := bytes.NewReader(fileData)
	_, err := s.client.PutObject(ctx, s.bucketName, objectKey, reader, int64(len(fileData)), minio.PutObjectOptions{
		ContentType:  contentType,
		UserMetadata: map[string]string{"x-amz-acl": "public-read"}, // Gắn thẻ public-read cho file
	})
	if err != nil {
		fmt.Printf("### MinioStorage.Upload ERROR: %v\n", err)
		return fmt.Errorf("failed to upload file: %w", err)
	}

	fmt.Printf("### MinioStorage.Upload SUCCESS: %s\n", objectKey)
	return nil
}

// Download triển khai phương thức Download của interface StorageRepository
func (s *MinioStorage) Download(ctx context.Context, objectKey string) ([]byte, error) {
	return s.GetObject(ctx, objectKey)
}

// GenerateUploadURL triển khai phương thức GenerateUploadURL của interface StorageRepository
func (s *MinioStorage) GenerateUploadURL(ctx context.Context, objectKey string, contentType string, expiry time.Duration) (string, error) {
	// Sử dụng expiry mặc định nếu không được cung cấp
	if expiry == 0 {
		expiry = s.config.PresignedExpiry
	}

	// Tạo presigned URL cho việc upload
	presignedURL, err := s.client.PresignedPutObject(ctx, s.bucketName, objectKey, expiry)
	if err != nil {
		return "", fmt.Errorf("failed to generate upload URL: %w", err)
	}

	return presignedURL.String(), nil
}

// GetURL triển khai phương thức GetURL của interface StorageRepository
func (s *MinioStorage) GetURL(ctx context.Context, objectKey string) (string, error) {
	if objectKey == "" {
		return "", repository.ErrInvalidObjectKey
	}

	// Kiểm tra xem object có tồn tại không
	exists, err := s.ObjectExists(ctx, objectKey)
	if err != nil {
		return "", fmt.Errorf("failed to check if object exists: %w", err)
	}
	if !exists {
		return "", fmt.Errorf("object does not exist: %s", objectKey)
	}

	// Nếu có domain CDN được cấu hình, sử dụng nó
	if cdnDomain := os.Getenv("CDN_DOMAIN"); cdnDomain != "" {
		return fmt.Sprintf("http://%s/%s", cdnDomain, objectKey), nil
	}

	// Fallback: Sử dụng URL trực tiếp cho truy cập public
	if os.Getenv("DOCKER_ENV") == "true" {
		return fmt.Sprintf("http://cdn.wn-api.local/%s", objectKey), nil
	}

	// URL trực tiếp cho các môi trường khác
	scheme := "http"
	if s.config.UseSSL {
		scheme = "https"
	}
	return fmt.Sprintf("%s://%s/%s/%s", scheme, s.config.Endpoint, s.bucketName, objectKey), nil
}

// Delete triển khai phương thức Delete của interface StorageRepository
func (s *MinioStorage) Delete(ctx context.Context, objectKey string) error {
	if objectKey == "" {
		return repository.ErrInvalidObjectKey
	}

	// Xóa một object từ bucket
	err := s.client.RemoveObject(ctx, s.bucketName, objectKey, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	return nil
}

// GetObject triển khai phương thức GetObject của interface StorageRepository
func (s *MinioStorage) GetObject(ctx context.Context, objectKey string) ([]byte, error) {
	if objectKey == "" {
		return nil, repository.ErrInvalidObjectKey
	}

	// Lấy object từ bucket
	object, err := s.client.GetObject(ctx, s.bucketName, objectKey, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get object: %w", err)
	}
	defer object.Close()

	// Đọc dữ liệu từ object
	objInfo, err := object.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get object info: %w", err)
	}

	// Đọc dữ liệu từ object
	buffer := make([]byte, objInfo.Size)
	_, err = object.Read(buffer)
	if err != nil && err != io.EOF {
		return nil, fmt.Errorf("failed to read object data: %w", err)
	}

	return buffer, nil
}

// ObjectExists triển khai phương thức ObjectExists của interface StorageRepository
func (s *MinioStorage) ObjectExists(ctx context.Context, objectKey string) (bool, error) {
	if objectKey == "" {
		return false, repository.ErrInvalidObjectKey
	}

	// Kiểm tra xem object có tồn tại không
	_, err := s.client.StatObject(ctx, s.bucketName, objectKey, minio.StatObjectOptions{})
	if err != nil {
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return false, nil
		}
		return false, fmt.Errorf("failed to check if object exists: %w", err)
	}

	return true, nil
}

// LocalStorage triển khai repository.StorageRepository interface sử dụng local file system
type LocalStorage struct {
	basePath string
	baseURL  string
}

// NewLocalStorage tạo một instance mới của LocalStorage
func NewLocalStorage(config configs.LocalStorage) (*LocalStorage, error) {
	// Tạo thư mục nếu không tồn tại
	err := os.MkdirAll(config.BasePath, 0755)
	if err != nil {
		return nil, err
	}

	return &LocalStorage{
		basePath: config.BasePath,
		baseURL:  config.BaseURL,
	}, nil
}

// Upload tải lên file
func (s *LocalStorage) Upload(ctx context.Context, fileData []byte, objectKey string, contentType string) error {
	fullPath := filepath.Join(s.basePath, objectKey)

	// Tạo thư mục cha nếu cần
	dir := filepath.Dir(fullPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// Ghi file
	return os.WriteFile(fullPath, fileData, 0644)
}

// Download tải xuống file
func (s *LocalStorage) Download(ctx context.Context, objectKey string) ([]byte, error) {
	fullPath := filepath.Join(s.basePath, objectKey)
	return os.ReadFile(fullPath)
}

// Delete xóa file
func (s *LocalStorage) Delete(ctx context.Context, objectKey string) error {
	fullPath := filepath.Join(s.basePath, objectKey)
	return os.Remove(fullPath)
}

// GetURL lấy URL để truy cập file
func (s *LocalStorage) GetURL(ctx context.Context, objectKey string) (string, error) {
	return fmt.Sprintf("%s/%s", s.baseURL, objectKey), nil
}

// FactoryImpl triển khai repository.Factory interface
type FactoryImpl struct {
	config configs.StorageConfig
}

// NewStorageFactory tạo một instance mới của repository.Factory
func NewStorageFactory(config configs.StorageConfig) repository.Factory {
	return &FactoryImpl{
		config: config,
	}
}

// NewStorageRepository triển khai phương thức NewStorageRepository của interface Factory
func (f *FactoryImpl) NewStorageRepository() repository.StorageRepository {
	if f.config.Type == "minio" || f.config.Type == "s3" {
		// Sử dụng Minio/S3 storage
		storage, err := NewMinioStorage(f.config.Minio)
		if err != nil {
			// Log lỗi và fallback về local storage
			fmt.Printf("Failed to create Minio storage: %v, falling back to local storage\n", err)
			return f.createLocalStorage()
		}
		return storage
	}

	// Mặc định sử dụng local storage
	return f.createLocalStorage()
}

// NewMediaRepository triển khai phương thức NewMediaRepository của interface Factory
func (f *FactoryImpl) NewMediaRepository() repository.MediaRepository {
	// Trong ví dụ này, chúng ta sẽ sử dụng MySQL repository
	// Thực tế, cần phải inject DB connection vào đây
	return nil
}

// NewMediaFolderRepository triển khai phương thức NewMediaFolderRepository của interface Factory
func (f *FactoryImpl) NewMediaFolderRepository() repository.MediaFolderRepository {
	// Trong ví dụ này, chúng ta sẽ sử dụng MySQL repository
	// Thực tế, cần phải inject DB connection vào đây
	return nil
}

// createLocalStorage tạo một local storage repository mới
func (f *FactoryImpl) createLocalStorage() repository.StorageRepository {
	storage, err := NewLocalStorage(f.config.Local)
	if err != nil {
		// Nếu không thể tạo local storage, log lỗi và trả về nil
		fmt.Printf("Failed to create local storage: %v\n", err)
		return nil
	}
	return storage
}
