package tracing

import (
	"context"
	"fmt"

	"github.com/opentracing/opentracing-go"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"github.com/webnew/wn-backend-v2/modules/media/configs"
)

// Global variables for tracers
var (
	jaegerTracer opentracing.Tracer
)

// SetJaegerTracer sets the global Jaeger tracer
func SetJaegerTracer(tracer opentracing.Tracer) {
	jaegerTracer = tracer
}

// StartSpan starts a new span based on the exporter type
func StartSpan(ctx context.Context, cfg *configs.TracingConfig, spanName string) (context.Context, trace.Span) {
	if cfg == nil || !cfg.Enabled {
		return ctx, nil
	}

	// Chỉ sử dụng OpenTelemetry trace để đảm bảo kiểu trả về nhất quán
	tracer := trace.NewNoopTracerProvider().Tracer(cfg.ServiceName)
	ctx, span := tracer.Start(ctx, spanName)
	return ctx, span
}

// EndSpan ends a span
func EndSpan(span trace.Span) {
	if span != nil {
		span.End()
	}
}

// AddAttribute adds an attribute to a span
func AddAttribute(span trace.Span, key string, value interface{}) {
	if span == nil {
		return
	}

	strValue := fmt.Sprintf("%v", value)
	span.SetAttributes(attribute.String(key, strValue))
}

// RecordError records an error in a span
func RecordError(span trace.Span, err error) {
	if span == nil || err == nil {
		return
	}

	span.RecordError(err)
	span.SetStatus(codes.Error, err.Error())
}

// TraceUpload traces a file upload operation
func TraceUpload(ctx context.Context, cfg *configs.TracingConfig, fileID, fileType string, fileSize int64) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, cfg, "media.upload")
	if span != nil {
		AddAttribute(span, "media.file_id", fileID)
		AddAttribute(span, "media.file_type", fileType)
		AddAttribute(span, "media.file_size", fileSize)
		AddAttribute(span, "media.operation", "upload")
	}
	return ctx, span
}

// TraceDownload traces a file download operation
func TraceDownload(ctx context.Context, cfg *configs.TracingConfig, fileID, fileType string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, cfg, "media.download")
	if span != nil {
		AddAttribute(span, "media.file_id", fileID)
		AddAttribute(span, "media.file_type", fileType)
		AddAttribute(span, "media.operation", "download")
	}
	return ctx, span
}

// TraceImageProcessing traces an image processing operation
func TraceImageProcessing(ctx context.Context, cfg *configs.TracingConfig, fileID, operation string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, cfg, "media.image_processing")
	if span != nil {
		AddAttribute(span, "media.file_id", fileID)
		AddAttribute(span, "media.operation", operation)
	}
	return ctx, span
}

// TraceStorageOperation traces a storage operation
func TraceStorageOperation(ctx context.Context, cfg *configs.TracingConfig, operation, storageType string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, cfg, "media.storage")
	if span != nil {
		AddAttribute(span, "media.operation", operation)
		AddAttribute(span, "media.storage_type", storageType)
	}
	return ctx, span
}

// TraceDBOperation traces a database operation
func TraceDBOperation(ctx context.Context, cfg *configs.TracingConfig, operation, table string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, cfg, "media.database")
	if span != nil {
		AddAttribute(span, "media.operation", operation)
		AddAttribute(span, "media.table", table)
	}
	return ctx, span
}

// GetSpanFromContext gets the current span from context
func GetSpanFromContext(ctx context.Context) trace.Span {
	if ctx == nil {
		return nil
	}

	span := trace.SpanFromContext(ctx)
	if span == nil || !span.IsRecording() {
		return nil
	}

	return span
}
