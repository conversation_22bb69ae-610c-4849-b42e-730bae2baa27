package tracing

import (
	"context"
	"fmt"

	"github.com/webnew/wn-backend-v2/modules/media/configs"
)

// TraceService is a helper function that wraps a service function with tracing
func TraceService(ctx context.Context, cfg *configs.TracingConfig, service, operation string, fn func(context.Context) error) error {
	// Kiểm tra xem tracing có được bật không
	if cfg == nil || !cfg.Enabled {
		// Nếu không bật, chỉ gọi hàm gốc mà không thêm tracing
		return fn(ctx)
	}

	// Tạo span mới
	ctx, span := StartSpan(ctx, cfg, service+"."+operation)
	defer EndSpan(span)

	// Gọi hàm gốc
	err := fn(ctx)
	if err != nil {
		RecordError(span, err)
	}

	return err
}

// TraceServiceWithResult is a helper function that wraps a service function with tracing and returns a result
func TraceServiceWithResult[T any](ctx context.Context, cfg *configs.TracingConfig, service, operation string, fn func(context.Context) (T, error)) (T, error) {
	var result T

	// Kiểm tra xem tracing có được bật không
	if cfg == nil || !cfg.Enabled {
		// Nếu không bật, chỉ gọi hàm gốc mà không thêm tracing
		return fn(ctx)
	}

	// Tạo span mới
	ctx, span := StartSpan(ctx, cfg, service+"."+operation)
	defer EndSpan(span)

	// Gọi hàm gốc
	result, err := fn(ctx)
	if err != nil {
		RecordError(span, err)
	}

	return result, err
}

// TraceRepository wraps a repository method call with tracing
func TraceRepository(ctx context.Context, cfg *configs.TracingConfig, repoName, methodName string, f func(context.Context) error) error {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	operationName := fmt.Sprintf("repository.%s.%s", repoName, methodName)
	ctx, span := StartSpan(ctx, cfg, operationName)
	defer EndSpan(span)

	if span != nil {
		AddAttribute(span, "repository", repoName)
		AddAttribute(span, "method", methodName)
	}

	err := f(ctx)
	if err != nil && span != nil {
		RecordError(span, err)
	}

	return err
}

// TraceRepositoryWithResult wraps a repository method call with tracing and returns a result
func TraceRepositoryWithResult[T any](ctx context.Context, cfg *configs.TracingConfig, repoName, methodName string, f func(context.Context) (T, error)) (T, error) {
	if cfg == nil || !cfg.Enabled {
		return f(ctx)
	}

	operationName := fmt.Sprintf("repository.%s.%s", repoName, methodName)
	ctx, span := StartSpan(ctx, cfg, operationName)
	defer EndSpan(span)

	if span != nil {
		AddAttribute(span, "repository", repoName)
		AddAttribute(span, "method", methodName)
	}

	result, err := f(ctx)
	if err != nil && span != nil {
		RecordError(span, err)
	}

	return result, err
}
