package tracing

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/media/configs"
)

// TracingMiddleware is a middleware for tracing HTTP requests
type TracingMiddleware struct {
	config *configs.TracingConfig
}

// NewTracingMiddleware creates a new tracing middleware
func NewTracingMiddleware(config *configs.TracingConfig) *TracingMiddleware {
	return &TracingMiddleware{
		config: config,
	}
}

// Middleware returns a gin middleware function
func (m *TracingMiddleware) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if m.config == nil || !m.config.Enabled {
			c.Next()
			return
		}

		// Start a new span
		operationName := "http.request"
		ctx, span := StartSimpleSpan(c.Request.Context(), m.config, operationName)
		defer EndSimpleSpan(span)

		// Add HTTP request attributes
		if span != nil {
			AddSimpleAttribute(span, "http.method", c.Request.Method)
			AddSimpleAttribute(span, "http.url", c.Request.URL.String())
			AddSimpleAttribute(span, "http.path", c.FullPath())
			AddSimpleAttribute(span, "http.user_agent", c.Request.UserAgent())
		}

		// Update the context
		c.Request = c.Request.WithContext(ctx)

		// Process the request
		c.Next()

		// Add response attributes
		if span != nil {
			statusCode := c.Writer.Status()
			AddSimpleAttribute(span, "http.status_code", statusCode)

			// Record error if status code is 4xx or 5xx
			if statusCode >= 400 {
				AddSimpleAttribute(span, "error", true)
				AddSimpleAttribute(span, "error.message", fmt.Sprintf("HTTP status code: %d", statusCode))
			}
		}
	}
}
