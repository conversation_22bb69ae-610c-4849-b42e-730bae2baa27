# Application Configuration
APP_NAME=wnapi
APP_VERSION=0.1.0
APP_ENV=development
LOG_LEVEL=info

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_TIMEOUT=30s
SERVER_READ_TIMEOUT=15s
SERVER_WRITE_TIMEOUT=15s
SERVER_MAX_HEADER_BYTES=1048576

# Database Configuration
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3307
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password
DB_DATABASE=wnapi
DB_MAX_OPEN_CONNS=20
DB_MAX_IDLE_CONNS=10
DB_CONN_MAX_LIFETIME=3600s
DB_MIGRATION_PATH=./migrations

# Module Configuration
MODULES_ENABLED=hello,auth

# Auth Module Configuration
AUTH_JWT_SECRET=your_very_secure_jwt_secret_key_here
AUTH_ACCESS_TOKEN_EXPIRY=30m
AUTH_REFRESH_TOKEN_EXPIRY=72h
AUTH_MESSAGE=Xin chào từ module Auth trong môi trường dev!

# Hello Module Configuration
HELLO_MESSAGE=Xin chào từ module Hello!
