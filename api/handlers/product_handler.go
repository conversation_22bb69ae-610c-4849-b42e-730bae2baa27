package handlers

import (
	"github.com/gin-gonic/gin"

	"wnapi/modules/product/service"
	"wnapi/internal/pkg/response"
)

// ProductHandler handles HTTP requests for products
type ProductHandler struct {
	productService service.ProductService
	// TODO: Add JWT service when auth package is available
	// jwtService     *auth.JWTService
}

// NewProductHandler creates a new product handler instance
func NewProductHandler(productService service.ProductService /* jwtService *auth.JWTService */) *ProductHandler {
	return &ProductHandler{
		productService: productService,
		// jwtService:     jwtService,
	}
}

// ListProducts handles product list requests
func (h *ProductHandler) ListProducts(c *gin.Context) {
	// TODO: Implement proper product list in subsequent tasks
	response.Success(c, []interface{}{}, nil)
}

// CreateProduct handles product create requests
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	// TODO: Implement proper product create in subsequent tasks
	response.Success(c, gin.H{"message": "Product created"}, nil)
}

// GetProduct handles product get requests
func (h *ProductHandler) GetProduct(c *gin.Context) {
	// TODO: Implement proper product get in subsequent tasks
	response.Success(c, gin.H{"id": c.Param("id")}, nil)
}

// UpdateProduct handles product update requests
func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	// TODO: Implement proper product update in subsequent tasks
	response.Success(c, gin.H{"message": "Product updated"}, nil)
}

// DeleteProduct handles product delete requests
func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	// TODO: Implement proper product delete in subsequent tasks
	response.Success(c, gin.H{"message": "Product deleted"}, nil)
}

// GetProductCategories handles product categories requests
func (h *ProductHandler) GetProductCategories(c *gin.Context) {
	// TODO: Implement proper product categories in subsequent tasks
	response.Success(c, []interface{}{}, nil)
}

// SearchProducts handles product search requests
func (h *ProductHandler) SearchProducts(c *gin.Context) {
	// TODO: Implement proper product search in subsequent tasks
	response.Success(c, []interface{}{}, nil)
}

// GetAllProducts handles get all products requests
func (h *ProductHandler) GetAllProducts(c *gin.Context) {
	// TODO: Implement proper get all products in subsequent tasks
	response.Success(c, []interface{}{}, nil)
}
